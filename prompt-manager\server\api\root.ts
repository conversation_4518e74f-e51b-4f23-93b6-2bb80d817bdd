/**
 * tRPC 根路由器
 * 汇总所有API路由模块
 */

import { createTRPCRouter } from '~/server/api/trpc';
import { categoryRouter } from '~/server/api/routers/category';
import { promptRouter } from '~/server/api/routers/prompt';
import { tagRouter } from '~/server/api/routers/tag';
import { searchRouter } from '~/server/api/routers/search';

/**
 * 应用主路由器
 * 包含所有API端点
 */
export const appRouter = createTRPCRouter({
  category: categoryRouter,
  prompt: promptRouter,
  tag: tagRouter,
  search: searchRouter,
});

// 导出路由器类型定义，用于客户端类型推断
export type AppRouter = typeof appRouter;
