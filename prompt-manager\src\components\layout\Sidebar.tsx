/**
 * 侧边栏组件
 * 包含分类列表、快捷操作等
 */

'use client';

import { useState } from 'react';
import Link from 'next/link';

interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
  count: number;
}

interface SidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  categories?: Category[];
  selectedCategoryId?: string;
  onCategorySelect?: (categoryId: string | null) => void;
  onNewPrompt?: () => void;
  onManageCategories?: () => void;
}

export default function Sidebar({
  isOpen = true,
  onClose,
  categories = [],
  selectedCategoryId,
  onCategorySelect,
  onNewPrompt,
  onManageCategories,
}: SidebarProps) {
  const [showAllCategories, setShowAllCategories] = useState(false);

  // 模拟数据
  const mockCategories: Category[] = [
    { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️', count: 12 },
    { id: '2', name: '代码生成', color: '#10B981', icon: '💻', count: 8 },
    { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐', count: 5 },
    { id: '4', name: '数据分析', color: '#EF4444', icon: '📊', count: 3 },
    { id: '5', name: '创意设计', color: '#8B5CF6', icon: '🎨', count: 7 },
  ];

  const displayCategories = categories.length > 0 ? categories : mockCategories;
  const visibleCategories = showAllCategories ? displayCategories : displayCategories.slice(0, 5);

  return (
    <>
      {/* 移动端遮罩 */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 md:hidden"
          onClick={onClose}
        />
      )}

      {/* 侧边栏 */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* 侧边栏头部 */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 md:hidden">
            <h2 className="text-lg font-semibold text-gray-900">菜单</h2>
            <button
              onClick={onClose}
              className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 快捷操作 */}
          <div className="p-4 border-b border-gray-200">
            <button
              onClick={onNewPrompt}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              + 新建提示词
            </button>
          </div>

          {/* 导航菜单 */}
          <nav className="flex-1 px-4 py-4 space-y-2">
            {/* 全部提示词 */}
            <button
              onClick={() => onCategorySelect?.(null)}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                selectedCategoryId === null
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              <svg className="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-7l2 2-2 2m2-2H9m10 7l2 2-2 2m2-2H9" />
              </svg>
              全部提示词
              <span className="ml-auto text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                {displayCategories.reduce((sum, cat) => sum + cat.count, 0)}
              </span>
            </button>

            {/* 收藏夹 */}
            <button className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              <svg className="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              收藏夹
              <span className="ml-auto text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">3</span>
            </button>

            {/* 最近使用 */}
            <button className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              <svg className="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              最近使用
            </button>

            {/* 分类标题 */}
            <div className="pt-4 pb-2">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">分类</h3>
            </div>

            {/* 分类列表 */}
            <div className="space-y-1">
              {visibleCategories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => onCategorySelect?.(category.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    selectedCategoryId === category.id
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <span className="mr-3 text-lg">{category.icon}</span>
                  <span className="flex-1 text-left">{category.name}</span>
                  <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                    {category.count}
                  </span>
                </button>
              ))}
            </div>

            {/* 显示更多/收起 */}
            {displayCategories.length > 5 && (
              <button
                onClick={() => setShowAllCategories(!showAllCategories)}
                className="w-full flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 transition-colors"
              >
                {showAllCategories ? '收起' : `显示更多 (${displayCategories.length - 5})`}
                <svg
                  className={`ml-1 h-4 w-4 transform transition-transform ${showAllCategories ? 'rotate-180' : ''}`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            )}

            {/* 管理分类 */}
            <button
              onClick={onManageCategories}
              className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <svg className="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              管理分类
            </button>
          </nav>

          {/* 底部信息 */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500 text-center">
              <p>提示词管理工具</p>
              <p className="mt-1">v1.0.0</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
