'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import EnhancedSearchBar from '@/components/ui/EnhancedSearchBar';
import PromptCard from '@/components/ui/PromptCard';
import PromptDetailModal from '@/components/ui/PromptDetailModal';
import PromptEditModal from '@/components/ui/PromptEditModal';
import CategoryManageModal from '@/components/ui/CategoryManageModal';
import ConfirmDialog from '@/components/ui/ConfirmDialog';
import { useToast } from '@/components/ui/ToastContainer';
import { api } from '~/lib/providers';
import {
  useSearchFilters,
  useAppActions,
  usePrompts,
  useCategories,
  useIsLoading,
  useError
} from '@/store/useAppStore';
import { useErrorHandler, useFormErrorHandler } from '@/contexts/ErrorContext';
import { ErrorDisplay } from '@/components/error/ErrorBoundary';
import { LoadingWithText, ListSkeleton, EmptyState, SearchLoading } from '@/components/ui/LoadingStates';

export default function Home() {
  const { showSuccess, showError, showInfo } = useToast();
  const { handleApiError } = useErrorHandler();
  const { handleSubmit, isSubmitting } = useFormErrorHandler();

  // 使用Zustand状态管理
  const searchFilters = useSearchFilters();
  const {
    setSearchFilters,
    setPrompts,
    setCategories,
    setLoading,
    setError,
    addPrompt,
    updatePrompt,
    deletePrompt,
    addSearchHistory
  } = useAppActions();

  // 使用tRPC hooks获取数据
  const { data: promptsData, isLoading, error, refetch } = api.prompt.getAll.useQuery({
    page: 1,
    limit: 20,
    categoryId: searchFilters.categoryId || undefined,
    search: searchFilters.query || undefined,
    tags: searchFilters.tags.length > 0 ? searchFilters.tags : undefined,
    sortBy: searchFilters.sortBy,
    sortOrder: searchFilters.sortOrder,
  }, {
    onSuccess: (data) => {
      // 将数据同步到Zustand store
      if (data?.prompts) {
        setPrompts(data.prompts);
      }
      setLoading(false);
      setError(null);
    },
    onError: (error) => {
      setLoading(false);
      setError(error.message);
      handleApiError(error, '获取提示词列表');
    },
  });

  // 获取分类数据
  const { data: categoriesData } = api.category.getAll.useQuery(undefined, {
    onSuccess: (data) => {
      if (data) {
        setCategories(data);
      }
    },
  });

  // 从Zustand store获取数据，如果API数据可用则使用API数据
  const storePrompts = usePrompts();
  const storeCategories = useCategories();
  const filteredPrompts = promptsData?.prompts || storePrompts;

  // tRPC mutations with Zustand integration
  const createPromptMutation = api.prompt.create.useMutation({
    onSuccess: (newPrompt) => {
      showSuccess('创建成功', '提示词已创建');
      addPrompt(newPrompt);
      refetch();
    },
    onError: (error) => {
      handleApiError(error, '创建提示词');
    },
  });

  const updatePromptMutation = api.prompt.update.useMutation({
    onSuccess: (updatedPrompt) => {
      showSuccess('更新成功', '提示词已更新');
      updatePrompt(updatedPrompt.id, updatedPrompt);
      refetch();
    },
    onError: (error) => {
      handleApiError(error, '更新提示词');
    },
  });

  const deletePromptMutation = api.prompt.delete.useMutation({
    onSuccess: (_, variables) => {
      showSuccess('删除成功', '提示词已删除');
      deletePrompt(variables.id);
      refetch();
    },
    onError: (error) => {
      handleApiError(error, '删除提示词');
    },
  });

  const incrementUsageMutation = api.prompt.incrementUsage.useMutation({
    onSuccess: (updatedPrompt) => {
      // 更新store中的使用次数
      updatePrompt(updatedPrompt.id, { usageCount: updatedPrompt.usageCount });
    },
  });

  // 模态框状态
  const [selectedPrompt, setSelectedPrompt] = useState<any>(null);
  const [editingPrompt, setEditingPrompt] = useState<any>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmAction, setConfirmAction] = useState<() => void>(() => {});

  const handleFiltersChange = (newFilters: any) => {
    setSearchFilters(newFilters);
  };

  const handlePromptEdit = (prompt: any) => {
    setEditingPrompt(prompt);
    setShowEditModal(true);
  };

  const handlePromptDelete = (promptId: string) => {
    setConfirmAction(() => () => {
      deletePromptMutation.mutate({ id: promptId });
    });
    setShowConfirmDialog(true);
  };

  const handlePromptCopy = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      showSuccess('复制成功', '提示词内容已复制到剪贴板');

      // 增加使用次数
      const prompt = filteredPrompts.find(p => p.content === content);
      if (prompt) {
        incrementUsageMutation.mutate({ id: prompt.id });
      }
    } catch (error) {
      showError('复制失败', '无法访问剪贴板');
    }
  };

  const handlePromptView = (prompt: any) => {
    setSelectedPrompt(prompt);
    setShowDetailModal(true);
  };

  const handleNewPrompt = () => {
    setEditingPrompt(null);
    setShowEditModal(true);
  };

  const handlePromptSave = async (promptData: any) => {
    const result = await handleSubmit(
      async () => {
        if (editingPrompt) {
          // 更新现有提示词
          return updatePromptMutation.mutateAsync({
            id: editingPrompt.id,
            ...promptData,
          });
        } else {
          // 创建新提示词
          return createPromptMutation.mutateAsync(promptData);
        }
      },
      {
        context: editingPrompt ? '更新提示词' : '创建提示词',
        onSuccess: () => {
          setShowEditModal(false);
        },
      }
    );
  };

  const handleCategorySave = (categoryData: any) => {
    console.log('保存分类:', categoryData);
    // 这里应该调用API保存分类
  };

  const handleCategoryUpdate = (id: string, categoryData: any) => {
    console.log('更新分类:', id, categoryData);
    // 这里应该调用API更新分类
  };

  const handleCategoryDelete = (id: string) => {
    console.log('删除分类:', id);
    // 这里应该调用API删除分类
  };

  return (
    <MainLayout
      onNewPrompt={handleNewPrompt}
      onManageCategories={() => setShowCategoryModal(true)}
    >
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">我的提示词</h1>
          <p className="mt-1 text-sm text-gray-600">
            管理和使用您的AI提示词库
          </p>
        </div>

        {/* 搜索和筛选 */}
        <EnhancedSearchBar
          filters={searchFilters}
          categories={storeCategories}
          tags={[]} // TODO: 从API获取标签数据
          onFiltersChange={handleFiltersChange}
          placeholder="搜索提示词标题、内容或描述..."
        />

        {/* 加载状态 */}
        {isLoading && (
          <div className="py-8">
            {searchFilters.query ? (
              <SearchLoading />
            ) : (
              <ListSkeleton count={6} className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" />
            )}
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <ErrorDisplay
            error={new Error(error.message || '加载失败')}
            onRetry={() => refetch()}
            className="my-6"
          />
        )}

        {/* 提示词列表 */}
        {!isLoading && !error && (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredPrompts.map((prompt) => (
              <PromptCard
                key={prompt.id}
                prompt={prompt}
                searchQuery={searchFilters.query}
                onEdit={handlePromptEdit}
                onDelete={handlePromptDelete}
                onCopy={handlePromptCopy}
                onView={handlePromptView}
              />
            ))}
          </div>
        )}

        {/* 空状态 */}
        {!isLoading && !error && filteredPrompts.length === 0 && (
          <EmptyState
            title={searchFilters.query ? '没有找到匹配的提示词' : '没有找到提示词'}
            description={searchFilters.query
              ? '尝试调整搜索条件或创建新的提示词'
              : '开始创建您的第一个提示词吧'
            }
            action={
              <button
                onClick={handleNewPrompt}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                新建提示词
              </button>
            }
          />
        )}
      </div>

      {/* 模态框组件 */}
      <PromptDetailModal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        prompt={selectedPrompt}
        onEdit={handlePromptEdit}
        onDelete={handlePromptDelete}
        onCopy={handlePromptCopy}
      />

      <PromptEditModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        prompt={editingPrompt}
        onSave={handlePromptSave}
      />

      <CategoryManageModal
        isOpen={showCategoryModal}
        onClose={() => setShowCategoryModal(false)}
        onSave={handleCategorySave}
        onUpdate={handleCategoryUpdate}
        onDelete={handleCategoryDelete}
      />

      <ConfirmDialog
        isOpen={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={confirmAction}
        message="此操作无法撤销，确定要继续吗？"
      />
    </MainLayout>
  );
}
