/**
 * 提示词详情模态框组件
 * 显示提示词的完整信息
 */

'use client';

import { useState } from 'react';
import Modal from './Modal';

interface Tag {
  id: string;
  name: string;
  color: string;
}

interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
}

interface Prompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: Category;
  tags: Tag[];
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

interface PromptDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  prompt: Prompt | null;
  onEdit?: (prompt: Prompt) => void;
  onDelete?: (promptId: string) => void;
  onCopy?: (content: string) => void;
}

export default function PromptDetailModal({
  isOpen,
  onClose,
  prompt,
  onEdit,
  onDelete,
  onCopy,
}: PromptDetailModalProps) {
  const [isCopied, setIsCopied] = useState(false);

  if (!prompt) return null;

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(prompt.content);
      onCopy?.(prompt.content);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  const handleEdit = () => {
    onEdit?.(prompt);
    onClose();
  };

  const handleDelete = () => {
    if (window.confirm('确定要删除这个提示词吗？')) {
      onDelete?.(prompt.id);
      onClose();
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="提示词详情"
      size="lg"
    >
      <div className="space-y-6">
        {/* 标题和描述 */}
        <div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">{prompt.title}</h2>
          {prompt.description && (
            <p className="text-gray-600">{prompt.description}</p>
          )}
        </div>

        {/* 分类和标签 */}
        <div className="flex flex-wrap items-center gap-2">
          {prompt.category && (
            <span
              className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white"
              style={{ backgroundColor: prompt.category.color }}
            >
              <span className="mr-1">{prompt.category.icon}</span>
              {prompt.category.name}
            </span>
          )}
          
          {prompt.tags.map((tag) => (
            <span
              key={tag.id}
              className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white"
              style={{ backgroundColor: tag.color }}
            >
              {tag.name}
            </span>
          ))}
        </div>

        {/* 提示词内容 */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold text-gray-900">提示词内容</h3>
            <button
              onClick={handleCopy}
              className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isCopied
                  ? 'bg-green-100 text-green-800'
                  : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
              }`}
            >
              {isCopied ? (
                <>
                  <svg className="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  已复制
                </>
              ) : (
                <>
                  <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  复制内容
                </>
              )}
            </button>
          </div>
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
              {prompt.content}
            </pre>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
          <div>
            <dt className="text-sm font-medium text-gray-500">使用次数</dt>
            <dd className="mt-1 text-lg font-semibold text-gray-900">{prompt.usageCount}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">创建时间</dt>
            <dd className="mt-1 text-sm text-gray-900">{formatDate(prompt.createdAt)}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">最后更新</dt>
            <dd className="mt-1 text-sm text-gray-900">{formatDate(prompt.updatedAt)}</dd>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            关闭
          </button>
          <button
            onClick={handleDelete}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            删除
          </button>
          <button
            onClick={handleEdit}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            编辑
          </button>
        </div>
      </div>
    </Modal>
  );
}
