/**
 * 统计页面
 * 展示提示词使用统计和数据管理功能
 */

'use client';

import { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import StatsPanel from '@/components/ui/StatsPanel';
import BatchImportModal from '@/components/ui/BatchImportModal';
import ExportModal from '@/components/ui/ExportModal';
import { useToast } from '@/components/ui/ToastContainer';

export default function StatsPage() {
  const [showImportModal, setShowImportModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const { showSuccess, showError } = useToast();

  const handleImport = async (data: any[]) => {
    // 这里应该调用API导入数据
    console.log('导入数据:', data);
    await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟API调用
  };

  const handleExport = async (options: any) => {
    // 这里应该调用API导出数据
    console.log('导出选项:', options);
    
    // 模拟生成导出文件
    const mockData = {
      prompts: [
        {
          title: '示例提示词1',
          content: '这是提示词内容...',
          category: '写作助手',
          tags: ['AI', '创意'],
          usageCount: 25,
          createdAt: '2024-01-15T10:00:00Z',
        },
        {
          title: '示例提示词2',
          content: '另一个提示词内容...',
          category: '代码生成',
          tags: ['编程', 'AI'],
          usageCount: 18,
          createdAt: '2024-01-10T09:00:00Z',
        },
      ],
      exportedAt: new Date().toISOString(),
      options,
    };

    // 根据格式生成文件
    let content: string;
    let filename: string;
    let mimeType: string;

    switch (options.format) {
      case 'json':
        content = JSON.stringify(mockData, null, 2);
        filename = 'prompts-export.json';
        mimeType = 'application/json';
        break;
      case 'csv':
        const csvHeaders = ['标题', '内容', '分类', '标签', '使用次数', '创建时间'];
        const csvRows = mockData.prompts.map(prompt => [
          prompt.title,
          prompt.content.replace(/"/g, '""'), // 转义双引号
          prompt.category,
          prompt.tags.join(';'),
          prompt.usageCount.toString(),
          new Date(prompt.createdAt).toLocaleDateString('zh-CN'),
        ]);
        content = [csvHeaders, ...csvRows]
          .map(row => row.map(cell => `"${cell}"`).join(','))
          .join('\n');
        filename = 'prompts-export.csv';
        mimeType = 'text/csv;charset=utf-8';
        break;
      case 'markdown':
        content = `# 提示词导出\n\n导出时间: ${new Date().toLocaleString('zh-CN')}\n\n`;
        mockData.prompts.forEach((prompt, index) => {
          content += `## ${index + 1}. ${prompt.title}\n\n`;
          content += `**分类**: ${prompt.category}\n\n`;
          content += `**标签**: ${prompt.tags.join(', ')}\n\n`;
          content += `**使用次数**: ${prompt.usageCount}\n\n`;
          content += `**内容**:\n\n${prompt.content}\n\n---\n\n`;
        });
        filename = 'prompts-export.md';
        mimeType = 'text/markdown;charset=utf-8';
        break;
      default:
        throw new Error('不支持的导出格式');
    }

    // 下载文件
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟处理时间
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* 页面标题和操作 */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">数据统计</h1>
            <p className="mt-1 text-sm text-gray-600">
              查看提示词使用统计和管理数据
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-3">
            <button
              onClick={() => setShowImportModal(true)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
              </svg>
              批量导入
            </button>
            <button
              onClick={() => setShowExportModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              导出数据
            </button>
          </div>
        </div>

        {/* 统计面板 */}
        <StatsPanel />

        {/* 快捷操作卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">批量导入</h3>
                <p className="text-sm text-gray-500 mt-1">
                  从JSON文件批量导入提示词
                </p>
                <button
                  onClick={() => setShowImportModal(true)}
                  className="mt-3 text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  开始导入 →
                </button>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">数据导出</h3>
                <p className="text-sm text-gray-500 mt-1">
                  导出为JSON、CSV或Markdown格式
                </p>
                <button
                  onClick={() => setShowExportModal(true)}
                  className="mt-3 text-sm text-green-600 hover:text-green-800 font-medium"
                >
                  开始导出 →
                </button>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">数据备份</h3>
                <p className="text-sm text-gray-500 mt-1">
                  定期备份您的提示词数据
                </p>
                <button className="mt-3 text-sm text-purple-600 hover:text-purple-800 font-medium">
                  设置备份 →
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 模态框 */}
      <BatchImportModal
        isOpen={showImportModal}
        onClose={() => setShowImportModal(false)}
        onImport={handleImport}
      />

      <ExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        onExport={handleExport}
      />
    </MainLayout>
  );
}
