/**
 * 关键词高亮组件
 * 在文本中高亮显示搜索关键词
 */

'use client';

import React from 'react';

interface HighlightTextProps {
  text: string;
  searchQuery: string;
  className?: string;
  highlightClassName?: string;
  caseSensitive?: boolean;
  maxLength?: number;
}

export default function HighlightText({
  text,
  searchQuery,
  className = '',
  highlightClassName = 'bg-yellow-200 text-yellow-900 px-1 rounded',
  caseSensitive = false,
  maxLength,
}: HighlightTextProps) {
  // 如果没有搜索查询，直接返回原文本
  if (!searchQuery || !searchQuery.trim()) {
    const displayText = maxLength && text.length > maxLength 
      ? text.substring(0, maxLength) + '...' 
      : text;
    return <span className={className}>{displayText}</span>;
  }

  const query = searchQuery.trim();
  const searchText = caseSensitive ? text : text.toLowerCase();
  const searchPattern = caseSensitive ? query : query.toLowerCase();

  // 截断文本（如果指定了最大长度）
  let displayText = text;
  let truncated = false;
  
  if (maxLength && text.length > maxLength) {
    // 尝试在关键词附近截断
    const firstMatchIndex = searchText.indexOf(searchPattern);
    if (firstMatchIndex !== -1) {
      // 计算截断的起始位置，尽量让关键词居中
      const contextLength = Math.floor((maxLength - query.length) / 2);
      const startIndex = Math.max(0, firstMatchIndex - contextLength);
      const endIndex = Math.min(text.length, startIndex + maxLength);
      
      displayText = text.substring(startIndex, endIndex);
      if (startIndex > 0) displayText = '...' + displayText;
      if (endIndex < text.length) displayText = displayText + '...';
      truncated = true;
    } else {
      // 如果没有找到关键词，从开头截断
      displayText = text.substring(0, maxLength) + '...';
      truncated = true;
    }
  }

  // 重新计算搜索文本（基于截断后的文本）
  const finalSearchText = caseSensitive ? displayText : displayText.toLowerCase();

  // 分割文本并高亮关键词
  const parts = [];
  let lastIndex = 0;
  let matchIndex = finalSearchText.indexOf(searchPattern);

  while (matchIndex !== -1) {
    // 添加匹配前的文本
    if (matchIndex > lastIndex) {
      parts.push(displayText.substring(lastIndex, matchIndex));
    }

    // 添加高亮的匹配文本
    const matchedText = displayText.substring(matchIndex, matchIndex + query.length);
    parts.push(
      <mark key={`highlight-${matchIndex}`} className={highlightClassName}>
        {matchedText}
      </mark>
    );

    lastIndex = matchIndex + query.length;
    matchIndex = finalSearchText.indexOf(searchPattern, lastIndex);
  }

  // 添加剩余的文本
  if (lastIndex < displayText.length) {
    parts.push(displayText.substring(lastIndex));
  }

  return <span className={className}>{parts}</span>;
}

/**
 * 多关键词高亮组件
 * 支持同时高亮多个关键词
 */
interface MultiHighlightTextProps {
  text: string;
  searchQueries: string[];
  className?: string;
  highlightClassNames?: string[];
  caseSensitive?: boolean;
  maxLength?: number;
}

export function MultiHighlightText({
  text,
  searchQueries,
  className = '',
  highlightClassNames = [
    'bg-yellow-200 text-yellow-900 px-1 rounded',
    'bg-blue-200 text-blue-900 px-1 rounded',
    'bg-green-200 text-green-900 px-1 rounded',
    'bg-purple-200 text-purple-900 px-1 rounded',
    'bg-pink-200 text-pink-900 px-1 rounded',
  ],
  caseSensitive = false,
  maxLength,
}: MultiHighlightTextProps) {
  // 过滤空查询
  const validQueries = searchQueries.filter(q => q && q.trim());
  
  if (validQueries.length === 0) {
    const displayText = maxLength && text.length > maxLength 
      ? text.substring(0, maxLength) + '...' 
      : text;
    return <span className={className}>{displayText}</span>;
  }

  // 截断文本
  let displayText = text;
  if (maxLength && text.length > maxLength) {
    displayText = text.substring(0, maxLength) + '...';
  }

  const searchText = caseSensitive ? displayText : displayText.toLowerCase();

  // 找到所有匹配位置
  const matches: Array<{ start: number; end: number; query: string; index: number }> = [];
  
  validQueries.forEach((query, queryIndex) => {
    const searchPattern = caseSensitive ? query.trim() : query.trim().toLowerCase();
    let matchIndex = searchText.indexOf(searchPattern);
    
    while (matchIndex !== -1) {
      matches.push({
        start: matchIndex,
        end: matchIndex + query.trim().length,
        query: query.trim(),
        index: queryIndex,
      });
      matchIndex = searchText.indexOf(searchPattern, matchIndex + 1);
    }
  });

  // 按位置排序并合并重叠的匹配
  matches.sort((a, b) => a.start - b.start);
  
  const mergedMatches: Array<{ start: number; end: number; queries: Array<{ query: string; index: number }> }> = [];
  
  matches.forEach(match => {
    const lastMerged = mergedMatches[mergedMatches.length - 1];
    
    if (lastMerged && match.start <= lastMerged.end) {
      // 重叠或相邻，合并
      lastMerged.end = Math.max(lastMerged.end, match.end);
      lastMerged.queries.push({ query: match.query, index: match.index });
    } else {
      // 新的匹配
      mergedMatches.push({
        start: match.start,
        end: match.end,
        queries: [{ query: match.query, index: match.index }],
      });
    }
  });

  // 构建结果
  const parts = [];
  let lastIndex = 0;

  mergedMatches.forEach((match, matchIndex) => {
    // 添加匹配前的文本
    if (match.start > lastIndex) {
      parts.push(displayText.substring(lastIndex, match.start));
    }

    // 添加高亮的匹配文本
    const matchedText = displayText.substring(match.start, match.end);
    const highlightClass = highlightClassNames[match.queries[0].index % highlightClassNames.length];
    
    parts.push(
      <mark key={`multi-highlight-${matchIndex}`} className={highlightClass}>
        {matchedText}
      </mark>
    );

    lastIndex = match.end;
  });

  // 添加剩余的文本
  if (lastIndex < displayText.length) {
    parts.push(displayText.substring(lastIndex));
  }

  return <span className={className}>{parts}</span>;
}

/**
 * 智能高亮组件
 * 自动检测搜索查询中的多个关键词并分别高亮
 */
interface SmartHighlightTextProps {
  text: string;
  searchQuery: string;
  className?: string;
  highlightClassName?: string;
  caseSensitive?: boolean;
  maxLength?: number;
  splitPattern?: RegExp;
}

export function SmartHighlightText({
  text,
  searchQuery,
  className = '',
  highlightClassName = 'bg-yellow-200 text-yellow-900 px-1 rounded',
  caseSensitive = false,
  maxLength,
  splitPattern = /[\s,，、]+/, // 默认按空格和常见分隔符分割
}: SmartHighlightTextProps) {
  if (!searchQuery || !searchQuery.trim()) {
    const displayText = maxLength && text.length > maxLength 
      ? text.substring(0, maxLength) + '...' 
      : text;
    return <span className={className}>{displayText}</span>;
  }

  // 分割搜索查询为多个关键词
  const keywords = searchQuery.trim().split(splitPattern).filter(k => k.length > 0);
  
  if (keywords.length === 1) {
    // 单个关键词，使用基础高亮
    return (
      <HighlightText
        text={text}
        searchQuery={keywords[0]}
        className={className}
        highlightClassName={highlightClassName}
        caseSensitive={caseSensitive}
        maxLength={maxLength}
      />
    );
  } else {
    // 多个关键词，使用多关键词高亮
    return (
      <MultiHighlightText
        text={text}
        searchQueries={keywords}
        className={className}
        highlightClassNames={[highlightClassName]}
        caseSensitive={caseSensitive}
        maxLength={maxLength}
      />
    );
  }
}
