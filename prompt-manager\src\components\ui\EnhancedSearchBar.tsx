/**
 * 增强的搜索栏组件
 * 支持搜索历史、关键词高亮、智能建议等功能
 */

'use client';

import { useState, useRef, useEffect } from 'react';
import { useSearchHistory, useRecentSearches, useAppActions } from '@/store/useAppStore';

interface Tag {
  id: string;
  name: string;
  color: string;
}

interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
}

interface SearchFilters {
  query: string;
  categoryId: string | null;
  tags: string[];
  sortBy: 'createdAt' | 'updatedAt' | 'usageCount' | 'title';
  sortOrder: 'asc' | 'desc';
}

interface EnhancedSearchBarProps {
  filters: SearchFilters;
  categories?: Category[];
  tags?: Tag[];
  onFiltersChange: (filters: SearchFilters) => void;
  placeholder?: string;
}

export default function EnhancedSearchBar({
  filters,
  categories = [],
  tags = [],
  onFiltersChange,
  placeholder = '搜索提示词...',
}: EnhancedSearchBarProps) {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [tagSearchQuery, setTagSearchQuery] = useState('');
  const filterRef = useRef<HTMLDivElement>(null);
  const historyRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 从Zustand store获取搜索历史
  const searchHistory = useSearchHistory();
  const recentSearches = useRecentSearches();
  const { addSearchHistory, removeSearchHistoryItem, clearSearchHistory } = useAppActions();

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setIsFilterOpen(false);
      }
      if (historyRef.current && !historyRef.current.contains(event.target as Node)) {
        setIsHistoryOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    onFiltersChange({
      ...filters,
      query: newQuery,
    });
    
    // 显示搜索历史（当输入框有焦点且有历史记录时）
    if (newQuery.length === 0 && recentSearches.length > 0) {
      setIsHistoryOpen(true);
    }
  };

  const handleQuerySubmit = (query: string) => {
    if (query.trim()) {
      addSearchHistory(query.trim());
      onFiltersChange({
        ...filters,
        query: query.trim(),
      });
    }
    setIsHistoryOpen(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleQuerySubmit(filters.query);
    } else if (e.key === 'ArrowDown' && recentSearches.length > 0) {
      setIsHistoryOpen(true);
    }
  };

  const handleHistoryItemClick = (query: string) => {
    onFiltersChange({
      ...filters,
      query,
    });
    addSearchHistory(query);
    setIsHistoryOpen(false);
    inputRef.current?.focus();
  };

  const handleRemoveHistoryItem = (e: React.MouseEvent, query: string) => {
    e.stopPropagation();
    removeSearchHistoryItem(query);
  };

  const handleClearHistory = () => {
    clearSearchHistory();
    setIsHistoryOpen(false);
  };

  const handleCategoryChange = (categoryId: string | null) => {
    onFiltersChange({
      ...filters,
      categoryId,
    });
  };

  const handleTagToggle = (tagId: string) => {
    const newTags = filters.tags.includes(tagId)
      ? filters.tags.filter(id => id !== tagId)
      : [...filters.tags, tagId];
    
    onFiltersChange({
      ...filters,
      tags: newTags,
    });
  };

  const handleSortChange = (sortBy: string, sortOrder: string) => {
    onFiltersChange({
      ...filters,
      sortBy: sortBy as any,
      sortOrder: sortOrder as any,
    });
  };

  // 筛选标签
  const filteredTags = tags.filter(tag =>
    tag.name.toLowerCase().includes(tagSearchQuery.toLowerCase())
  );

  // 筛选搜索历史
  const filteredHistory = recentSearches.filter(query =>
    query.toLowerCase().includes(filters.query.toLowerCase())
  );

  return (
    <div className="relative">
      {/* 主搜索区域 */}
      <div className="flex gap-4 mb-6">
        {/* 搜索输入框 */}
        <div className="flex-1 relative" ref={historyRef}>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              ref={inputRef}
              type="text"
              value={filters.query}
              onChange={handleQueryChange}
              onKeyDown={handleKeyDown}
              onFocus={() => {
                if (filters.query.length === 0 && recentSearches.length > 0) {
                  setIsHistoryOpen(true);
                }
              }}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder={placeholder}
            />
          </div>

          {/* 搜索历史下拉框 */}
          {isHistoryOpen && (recentSearches.length > 0 || filters.query.length > 0) && (
            <div className="absolute z-50 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
              {filteredHistory.length > 0 && (
                <>
                  <div className="px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                    最近搜索
                  </div>
                  {filteredHistory.map((query, index) => (
                    <div
                      key={index}
                      className="group flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer"
                      onClick={() => handleHistoryItemClick(query)}
                    >
                      <div className="flex items-center">
                        <svg className="h-4 w-4 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="text-sm text-gray-900">{query}</span>
                      </div>
                      <button
                        onClick={(e) => handleRemoveHistoryItem(e, query)}
                        className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-200 rounded"
                      >
                        <svg className="h-3 w-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ))}
                  {recentSearches.length > 0 && (
                    <div className="border-t">
                      <button
                        onClick={handleClearHistory}
                        className="w-full px-3 py-2 text-xs text-gray-500 hover:bg-gray-50 text-left"
                      >
                        清除搜索历史
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
          )}
        </div>

        {/* 筛选按钮 */}
        <div className="relative" ref={filterRef}>
          <button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
              (filters.categoryId || filters.tags.length > 0) ? 'ring-2 ring-blue-500 border-blue-500' : ''
            }`}
          >
            <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>
            筛选
            {(filters.categoryId || filters.tags.length > 0) && (
              <span className="ml-1 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {(filters.categoryId ? 1 : 0) + filters.tags.length}
              </span>
            )}
          </button>

          {/* 筛选下拉框 */}
          {isFilterOpen && (
            <div className="absolute right-0 z-50 mt-2 w-80 bg-white shadow-lg rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div className="p-4 space-y-4">
                {/* 分类筛选 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">分类</label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="category"
                        checked={filters.categoryId === null}
                        onChange={() => handleCategoryChange(null)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">全部分类</span>
                    </label>
                    {categories.map((category) => (
                      <label key={category.id} className="flex items-center">
                        <input
                          type="radio"
                          name="category"
                          checked={filters.categoryId === category.id}
                          onChange={() => handleCategoryChange(category.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700 flex items-center">
                          <span className="mr-1">{category.icon}</span>
                          {category.name}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* 标签筛选 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">标签</label>
                  <input
                    type="text"
                    value={tagSearchQuery}
                    onChange={(e) => setTagSearchQuery(e.target.value)}
                    placeholder="搜索标签..."
                    className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 mb-2"
                  />
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {filteredTags.map((tag) => (
                      <label key={tag.id} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.tags.includes(tag.id)}
                          onChange={() => handleTagToggle(tag.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span
                          className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                          style={{ backgroundColor: tag.color + '20', color: tag.color }}
                        >
                          {tag.name}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* 排序选项 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">排序</label>
                  <div className="grid grid-cols-2 gap-2">
                    <select
                      value={filters.sortBy}
                      onChange={(e) => handleSortChange(e.target.value, filters.sortOrder)}
                      className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="updatedAt">更新时间</option>
                      <option value="createdAt">创建时间</option>
                      <option value="usageCount">使用次数</option>
                      <option value="title">标题</option>
                    </select>
                    <select
                      value={filters.sortOrder}
                      onChange={(e) => handleSortChange(filters.sortBy, e.target.value)}
                      className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="desc">降序</option>
                      <option value="asc">升序</option>
                    </select>
                  </div>
                </div>

                {/* 重置按钮 */}
                <div className="pt-2 border-t">
                  <button
                    onClick={() => {
                      onFiltersChange({
                        query: '',
                        categoryId: null,
                        tags: [],
                        sortBy: 'updatedAt',
                        sortOrder: 'desc',
                      });
                      setTagSearchQuery('');
                    }}
                    className="w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-md"
                  >
                    重置筛选
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 活动筛选标签 */}
      {(filters.categoryId || filters.tags.length > 0) && (
        <div className="flex flex-wrap gap-2 mb-4">
          {filters.categoryId && (
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
              {categories.find(c => c.id === filters.categoryId)?.name || '未知分类'}
              <button
                onClick={() => handleCategoryChange(null)}
                className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-200"
              >
                <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </span>
          )}
          {filters.tags.map((tagId) => {
            const tag = tags.find(t => t.id === tagId);
            if (!tag) return null;
            return (
              <span
                key={tagId}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                style={{ backgroundColor: tag.color + '20', color: tag.color }}
              >
                {tag.name}
                <button
                  onClick={() => handleTagToggle(tagId)}
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-black hover:bg-opacity-10"
                >
                  <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </span>
            );
          })}
        </div>
      )}
    </div>
  );
}
