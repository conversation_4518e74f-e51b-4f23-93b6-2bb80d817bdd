/**
 * Next.js中间件
 * 处理路由保护和认证检查
 */

import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 获取认证token (简化版本，实际应该从session中获取)
  const authToken = request.cookies.get('next-auth.session-token')?.value ||
                   request.cookies.get('__Secure-next-auth.session-token')?.value;

  // 如果用户已登录但访问认证页面，重定向到首页
  if (authToken && pathname.startsWith('/auth/')) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // 如果用户未登录但访问受保护的页面，重定向到登录页
  if (!authToken && isProtectedRoute(pathname)) {
    const signInUrl = new URL('/auth/signin', request.url);
    signInUrl.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(signInUrl);
  }

  return NextResponse.next();
}

/**
 * 检查是否为公开路由
 */
function isPublicRoute(pathname: string): boolean {
  const publicRoutes = [
    '/',
    '/api/auth',
    '/auth/signin',
    '/auth/signup',
    '/auth/forgot-password',
    '/terms',
    '/privacy',
  ];
  
  return publicRoutes.some(route => 
    pathname === route || pathname.startsWith(`${route}/`)
  );
}

/**
 * 检查是否为受保护的路由
 */
function isProtectedRoute(pathname: string): boolean {
  const protectedRoutes = [
    '/dashboard',
    '/profile',
    '/settings',
    '/admin',
  ];
  
  return protectedRoutes.some(route => 
    pathname.startsWith(route)
  );
}

// 配置中间件匹配的路径
export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api routes (除了 /api/auth)
     * - _next/static (静态文件)
     * - _next/image (图片优化)
     * - favicon.ico
     * - public文件夹中的文件
     */
    '/((?!api(?!/auth)|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
