/**
 * tRPC 客户端配置
 * 用于在客户端调用tRPC API，集成缓存策略
 */

import { createTRPCReact } from '@trpc/react-query';
import { httpBatchLink } from '@trpc/client';
import { QueryClient } from '@tanstack/react-query';
import { type AppRouter } from '~/server/api/root';

// 创建带缓存配置的QueryClient
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // 缓存时间 5 分钟
      staleTime: 5 * 60 * 1000,
      // 数据在后台保持 10 分钟
      gcTime: 10 * 60 * 1000,
      // 重试配置
      retry: (failureCount, error: any) => {
        // 4xx 错误不重试
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // 最多重试 3 次
        return failureCount < 3;
      },
      // 重新获取配置
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      // 变更失败时重试 1 次
      retry: 1,
    },
  },
});

// 创建tRPC客户端
export const api = createTRPCReact<AppRouter>();

// tRPC客户端配置
export function getTRPCClient() {
  return api.createClient({
    links: [
      httpBatchLink({
        url: '/api/trpc',
        // 批量请求配置
        maxURLLength: 2083,
      }),
    ],
  });
}
