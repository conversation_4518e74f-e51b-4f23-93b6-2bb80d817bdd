/**
 * 数据库种子脚本
 * 用于初始化测试数据
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('开始初始化数据库...');

  // 创建测试用户
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: 'temp-user-id',
      email: '<EMAIL>',
      name: '临时用户',
    },
  });

  console.log('创建用户:', user);

  // 创建分类
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { name_userId: { name: '写作助手', userId: user.id } },
      update: {},
      create: {
        name: '写作助手',
        description: '帮助写作的AI提示词',
        color: '#3B82F6',
        icon: '✍️',
        userId: user.id,
      },
    }),
    prisma.category.upsert({
      where: { name_userId: { name: '代码生成', userId: user.id } },
      update: {},
      create: {
        name: '代码生成',
        description: '代码生成相关的提示词',
        color: '#10B981',
        icon: '💻',
        userId: user.id,
      },
    }),
    prisma.category.upsert({
      where: { name_userId: { name: '翻译工具', userId: user.id } },
      update: {},
      create: {
        name: '翻译工具',
        description: '翻译相关的提示词',
        color: '#F59E0B',
        icon: '🌐',
        userId: user.id,
      },
    }),
  ]);

  console.log('创建分类:', categories);

  // 创建标签
  const tags = await Promise.all([
    prisma.tag.upsert({
      where: { name: 'AI' },
      update: {},
      create: {
        name: 'AI',
        color: '#3B82F6',
      },
    }),
    prisma.tag.upsert({
      where: { name: '编程' },
      update: {},
      create: {
        name: '编程',
        color: '#10B981',
      },
    }),
    prisma.tag.upsert({
      where: { name: '创意' },
      update: {},
      create: {
        name: '创意',
        color: '#F59E0B',
      },
    }),
    prisma.tag.upsert({
      where: { name: '商务' },
      update: {},
      create: {
        name: '商务',
        color: '#EF4444',
      },
    }),
  ]);

  console.log('创建标签:', tags);

  // 创建提示词
  const prompts = await Promise.all([
    prisma.prompt.upsert({
      where: { id: 'prompt-1' },
      update: {},
      create: {
        id: 'prompt-1',
        title: '写作助手 - 文章大纲生成',
        content: '请帮我为以下主题创建一个详细的文章大纲：[主题]\n\n要求：\n1. 包含引言、主体和结论\n2. 主体部分至少3个要点\n3. 每个要点包含2-3个子点\n4. 提供吸引人的标题建议',
        description: '帮助用户快速生成文章大纲，提高写作效率',
        categoryId: categories[0].id,
        userId: user.id,
        usageCount: 25,
      },
    }),
    prisma.prompt.upsert({
      where: { id: 'prompt-2' },
      update: {},
      create: {
        id: 'prompt-2',
        title: '代码生成 - React组件模板',
        content: '请为我生成一个React函数组件，要求：\n\n组件名：[组件名]\n功能：[功能描述]\n\n请包含：\n- TypeScript类型定义\n- Props接口\n- 基本的JSX结构\n- 简单的样式类名\n- 必要的注释',
        description: '快速生成React组件的基础模板代码',
        categoryId: categories[1].id,
        userId: user.id,
        usageCount: 18,
      },
    }),
    prisma.prompt.upsert({
      where: { id: 'prompt-3' },
      update: {},
      create: {
        id: 'prompt-3',
        title: '翻译工具 - 专业文档翻译',
        content: '请将以下内容翻译成[目标语言]，要求：\n\n1. 保持专业术语的准确性\n2. 语言流畅自然\n3. 保留原文格式\n4. 如有专业术语，请在括号内标注原文\n\n原文：\n[待翻译内容]',
        description: '专业文档翻译，保持术语准确性和格式完整',
        categoryId: categories[2].id,
        userId: user.id,
        usageCount: 12,
      },
    }),
  ]);

  console.log('创建提示词:', prompts);

  // 创建提示词标签关联
  await Promise.all([
    // 第一个提示词的标签
    prisma.promptTag.upsert({
      where: { promptId_tagId: { promptId: prompts[0].id, tagId: tags[0].id } },
      update: {},
      create: {
        promptId: prompts[0].id,
        tagId: tags[0].id,
      },
    }),
    prisma.promptTag.upsert({
      where: { promptId_tagId: { promptId: prompts[0].id, tagId: tags[2].id } },
      update: {},
      create: {
        promptId: prompts[0].id,
        tagId: tags[2].id,
      },
    }),
    // 第二个提示词的标签
    prisma.promptTag.upsert({
      where: { promptId_tagId: { promptId: prompts[1].id, tagId: tags[0].id } },
      update: {},
      create: {
        promptId: prompts[1].id,
        tagId: tags[0].id,
      },
    }),
    prisma.promptTag.upsert({
      where: { promptId_tagId: { promptId: prompts[1].id, tagId: tags[1].id } },
      update: {},
      create: {
        promptId: prompts[1].id,
        tagId: tags[1].id,
      },
    }),
    // 第三个提示词的标签
    prisma.promptTag.upsert({
      where: { promptId_tagId: { promptId: prompts[2].id, tagId: tags[3].id } },
      update: {},
      create: {
        promptId: prompts[2].id,
        tagId: tags[3].id,
      },
    }),
  ]);

  console.log('数据库初始化完成！');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
