/**
 * 数据获取hooks
 * 集成tRPC和Zustand状态管理
 */

'use client';

import { useEffect } from 'react';
import { useAppStore, useAppActions } from '@/store/useAppStore';
// import { trpc } from '@/lib/trpc';

// 缓存时间常量 (5分钟)
const CACHE_DURATION = 5 * 60 * 1000;

/**
 * 检查数据是否需要刷新
 */
function shouldRefreshData(lastFetchTime: number): boolean {
  return Date.now() - lastFetchTime > CACHE_DURATION;
}

/**
 * 提示词数据hook
 */
export function usePromptsData() {
  const prompts = useAppStore((state) => state.prompts);
  const lastFetchTime = useAppStore((state) => state.lastFetchTime);
  const isLoading = useAppStore((state) => state.isLoading);
  const error = useAppStore((state) => state.error);
  const { setPrompts, setLoading, setError, updateLastFetchTime } = useAppActions();

  // 模拟tRPC查询
  const shouldFetch = prompts.length === 0 || shouldRefreshData(lastFetchTime);

  useEffect(() => {
    if (shouldFetch && !isLoading) {
      fetchPrompts();
    }
  }, [shouldFetch, isLoading]);

  const fetchPrompts = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟数据
      const mockPrompts = [
        {
          id: '1',
          title: '写作助手 - 文章大纲生成',
          content: '请帮我为以下主题创建一个详细的文章大纲：[主题]\n\n要求：\n1. 包含引言、主体和结论\n2. 主体部分至少3个要点\n3. 每个要点包含2-3个子点\n4. 提供吸引人的标题建议',
          description: '帮助用户快速生成文章大纲，提高写作效率',
          category: { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️' },
          tags: [
            { id: '1', name: 'AI', color: '#3B82F6' },
            { id: '3', name: '创意', color: '#F59E0B' },
          ],
          usageCount: 25,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-20T15:30:00Z',
        },
        {
          id: '2',
          title: '代码生成 - React组件模板',
          content: '请为我生成一个React函数组件，要求：\n\n组件名：[组件名]\n功能：[功能描述]\n\n请包含：\n- TypeScript类型定义\n- Props接口\n- 基本的JSX结构\n- 简单的样式类名\n- 必要的注释',
          description: '快速生成React组件的基础模板代码',
          category: { id: '2', name: '代码生成', color: '#10B981', icon: '💻' },
          tags: [
            { id: '2', name: '编程', color: '#10B981' },
            { id: '1', name: 'AI', color: '#3B82F6' },
          ],
          usageCount: 18,
          createdAt: '2024-01-10T09:00:00Z',
          updatedAt: '2024-01-18T11:20:00Z',
        },
        {
          id: '3',
          title: '翻译工具 - 专业文档翻译',
          content: '请将以下内容翻译成[目标语言]，要求：\n\n1. 保持专业术语的准确性\n2. 语言流畅自然\n3. 保留原文格式\n4. 如有专业术语，请在括号内标注原文\n\n原文：\n[待翻译内容]',
          description: '专业文档翻译，保持术语准确性和格式完整',
          category: { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐' },
          tags: [
            { id: '4', name: '商务', color: '#EF4444' },
          ],
          usageCount: 12,
          createdAt: '2024-01-05T14:00:00Z',
          updatedAt: '2024-01-15T16:45:00Z',
        },
      ];

      setPrompts(mockPrompts);
      updateLastFetchTime();
    } catch (err) {
      setError('获取提示词数据失败');
      console.error('获取提示词失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const refetch = () => {
    fetchPrompts();
  };

  return {
    prompts,
    isLoading,
    error,
    refetch,
  };
}

/**
 * 分类数据hook
 */
export function useCategoriesData() {
  const categories = useAppStore((state) => state.categories);
  const { setCategories } = useAppActions();

  useEffect(() => {
    if (categories.length === 0) {
      fetchCategories();
    }
  }, [categories.length]);

  const fetchCategories = async () => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 模拟数据
      const mockCategories = [
        { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️' },
        { id: '2', name: '代码生成', color: '#10B981', icon: '💻' },
        { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐' },
        { id: '4', name: '数据分析', color: '#EF4444', icon: '📊' },
        { id: '5', name: '创意设计', color: '#8B5CF6', icon: '🎨' },
      ];

      setCategories(mockCategories);
    } catch (err) {
      console.error('获取分类失败:', err);
    }
  };

  const refetch = () => {
    fetchCategories();
  };

  return {
    categories,
    refetch,
  };
}

/**
 * 标签数据hook
 */
export function useTagsData() {
  const tags = useAppStore((state) => state.tags);
  const { setTags } = useAppActions();

  useEffect(() => {
    if (tags.length === 0) {
      fetchTags();
    }
  }, [tags.length]);

  const fetchTags = async () => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 模拟数据
      const mockTags = [
        { id: '1', name: 'AI', color: '#3B82F6' },
        { id: '2', name: '编程', color: '#10B981' },
        { id: '3', name: '创意', color: '#F59E0B' },
        { id: '4', name: '商务', color: '#EF4444' },
        { id: '5', name: '学习', color: '#8B5CF6' },
        { id: '6', name: '工具', color: '#06B6D4' },
      ];

      setTags(mockTags);
    } catch (err) {
      console.error('获取标签失败:', err);
    }
  };

  const refetch = () => {
    fetchTags();
  };

  return {
    tags,
    refetch,
  };
}

/**
 * 搜索hook
 */
export function useSearch() {
  const prompts = useAppStore((state) => state.prompts);
  const searchFilters = useAppStore((state) => state.searchFilters);
  const { setSearchFilters } = useAppActions();

  // 过滤和排序提示词
  const filteredPrompts = prompts.filter(prompt => {
    // 文本搜索
    if (searchFilters.query) {
      const query = searchFilters.query.toLowerCase();
      const matchesTitle = prompt.title.toLowerCase().includes(query);
      const matchesContent = prompt.content.toLowerCase().includes(query);
      const matchesDescription = prompt.description?.toLowerCase().includes(query);
      
      if (!matchesTitle && !matchesContent && !matchesDescription) {
        return false;
      }
    }

    // 分类筛选
    if (searchFilters.categoryId && prompt.category?.id !== searchFilters.categoryId) {
      return false;
    }

    // 标签筛选
    if (searchFilters.tags.length > 0) {
      const promptTagIds = prompt.tags.map(tag => tag.id);
      const hasMatchingTag = searchFilters.tags.some(tagId => 
        promptTagIds.includes(tagId)
      );
      if (!hasMatchingTag) {
        return false;
      }
    }

    return true;
  }).sort((a, b) => {
    const { sortBy, sortOrder } = searchFilters;
    let comparison = 0;

    switch (sortBy) {
      case 'title':
        comparison = a.title.localeCompare(b.title);
        break;
      case 'createdAt':
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        break;
      case 'updatedAt':
        comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime();
        break;
      case 'usageCount':
        comparison = a.usageCount - b.usageCount;
        break;
      default:
        comparison = 0;
    }

    return sortOrder === 'asc' ? comparison : -comparison;
  });

  return {
    filteredPrompts,
    searchFilters,
    setSearchFilters,
  };
}

/**
 * 数据初始化hook
 */
export function useDataInitialization() {
  const { refetch: refetchPrompts } = usePromptsData();
  const { refetch: refetchCategories } = useCategoriesData();
  const { refetch: refetchTags } = useTagsData();

  const initializeData = () => {
    refetchPrompts();
    refetchCategories();
    refetchTags();
  };

  return {
    initializeData,
  };
}
