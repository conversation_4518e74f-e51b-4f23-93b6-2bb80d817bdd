/**
 * 数据导出模态框组件
 * 支持多种格式导出提示词数据
 */

'use client';

import { useState } from 'react';
import Modal from './Modal';
import { useToast } from './ToastContainer';

interface ExportOptions {
  format: 'json' | 'csv' | 'markdown';
  includeCategories: boolean;
  includeTags: boolean;
  includeStats: boolean;
  dateRange: 'all' | '30d' | '90d' | '1y';
  selectedCategories: string[];
}

interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (options: ExportOptions) => Promise<void>;
  categories?: Array<{ id: string; name: string; color: string; icon: string }>;
}

export default function ExportModal({
  isOpen,
  onClose,
  onExport,
  categories = [],
}: ExportModalProps) {
  const [options, setOptions] = useState<ExportOptions>({
    format: 'json',
    includeCategories: true,
    includeTags: true,
    includeStats: false,
    dateRange: 'all',
    selectedCategories: [],
  });
  const [isExporting, setIsExporting] = useState(false);
  const { showSuccess, showError } = useToast();

  // 模拟分类数据
  const mockCategories = [
    { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️' },
    { id: '2', name: '代码生成', color: '#10B981', icon: '💻' },
    { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐' },
  ];

  const displayCategories = categories.length > 0 ? categories : mockCategories;

  const handleExport = async () => {
    setIsExporting(true);
    try {
      await onExport(options);
      showSuccess('导出成功', '数据已下载到您的设备');
      onClose();
    } catch (error) {
      showError('导出失败', '请稍后重试');
    } finally {
      setIsExporting(false);
    }
  };

  const handleCategoryToggle = (categoryId: string) => {
    setOptions(prev => ({
      ...prev,
      selectedCategories: prev.selectedCategories.includes(categoryId)
        ? prev.selectedCategories.filter(id => id !== categoryId)
        : [...prev.selectedCategories, categoryId],
    }));
  };

  const selectAllCategories = () => {
    setOptions(prev => ({
      ...prev,
      selectedCategories: displayCategories.map(cat => cat.id),
    }));
  };

  const deselectAllCategories = () => {
    setOptions(prev => ({
      ...prev,
      selectedCategories: [],
    }));
  };

  const getFormatDescription = (format: string) => {
    switch (format) {
      case 'json':
        return '结构化数据格式，适合程序处理和备份';
      case 'csv':
        return '表格格式，适合在Excel等软件中查看';
      case 'markdown':
        return '文档格式，适合阅读和分享';
      default:
        return '';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="导出数据"
      size="lg"
    >
      <div className="space-y-6">
        {/* 导出格式 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            导出格式
          </label>
          <div className="space-y-3">
            {[
              { value: 'json', label: 'JSON', icon: '📄' },
              { value: 'csv', label: 'CSV', icon: '📊' },
              { value: 'markdown', label: 'Markdown', icon: '📝' },
            ].map((format) => (
              <label key={format.value} className="flex items-start">
                <input
                  type="radio"
                  name="format"
                  value={format.value}
                  checked={options.format === format.value}
                  onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as any }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 mt-1"
                />
                <div className="ml-3">
                  <div className="flex items-center">
                    <span className="mr-2">{format.icon}</span>
                    <span className="text-sm font-medium text-gray-900">{format.label}</span>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    {getFormatDescription(format.value)}
                  </p>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* 包含内容 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            包含内容
          </label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={options.includeCategories}
                onChange={(e) => setOptions(prev => ({ ...prev, includeCategories: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-900">分类信息</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={options.includeTags}
                onChange={(e) => setOptions(prev => ({ ...prev, includeTags: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-900">标签信息</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={options.includeStats}
                onChange={(e) => setOptions(prev => ({ ...prev, includeStats: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-900">使用统计</span>
            </label>
          </div>
        </div>

        {/* 时间范围 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            时间范围
          </label>
          <select
            value={options.dateRange}
            onChange={(e) => setOptions(prev => ({ ...prev, dateRange: e.target.value as any }))}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">全部时间</option>
            <option value="30d">最近30天</option>
            <option value="90d">最近90天</option>
            <option value="1y">最近1年</option>
          </select>
        </div>

        {/* 分类筛选 */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className="block text-sm font-medium text-gray-700">
              选择分类
            </label>
            <div className="space-x-2">
              <button
                type="button"
                onClick={selectAllCategories}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                全选
              </button>
              <button
                type="button"
                onClick={deselectAllCategories}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                清空
              </button>
            </div>
          </div>
          <div className="max-h-32 overflow-y-auto border border-gray-200 rounded-md p-3 space-y-2">
            {displayCategories.map((category) => (
              <label key={category.id} className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.selectedCategories.includes(category.id)}
                  onChange={() => handleCategoryToggle(category.id)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 flex items-center text-sm text-gray-900">
                  <span className="mr-1">{category.icon}</span>
                  {category.name}
                </span>
              </label>
            ))}
          </div>
          <p className="mt-2 text-sm text-gray-500">
            {options.selectedCategories.length === 0 
              ? '未选择分类，将导出所有分类的数据'
              : `已选择 ${options.selectedCategories.length} 个分类`
            }
          </p>
        </div>

        {/* 预览信息 */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-2">导出预览</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <p>• 格式: {options.format.toUpperCase()}</p>
            <p>• 时间范围: {
              options.dateRange === 'all' ? '全部时间' :
              options.dateRange === '30d' ? '最近30天' :
              options.dateRange === '90d' ? '最近90天' : '最近1年'
            }</p>
            <p>• 分类: {
              options.selectedCategories.length === 0 ? '全部分类' :
              `${options.selectedCategories.length} 个分类`
            }</p>
            <p>• 包含: {[
              options.includeCategories && '分类信息',
              options.includeTags && '标签信息',
              options.includeStats && '使用统计',
            ].filter(Boolean).join('、') || '仅基本信息'}</p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            onClick={onClose}
            disabled={isExporting}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            取消
          </button>
          <button
            onClick={handleExport}
            disabled={isExporting}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isExporting ? '导出中...' : '开始导出'}
          </button>
        </div>
      </div>
    </Modal>
  );
}
