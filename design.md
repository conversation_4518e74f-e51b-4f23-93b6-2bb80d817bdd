# 提示词管理工具设计文档

## 项目概述

本项目是一个现代化的提示词管理工具，采用全中文界面，为个人用户提供高效的AI提示词管理体验。系统采用Next.js 15 + TypeScript + tRPC + Prisma + PostgreSQL的全栈架构，结合daisyUI组件库打造精美的用户界面。

## 技术架构

### 前端技术栈
- **核心框架**: Next.js 15 (App Router)
- **开发语言**: TypeScript
- **UI样式系统**: Tailwind CSS V4
- **UI组件库**: daisyUI V5
- **状态管理**: Zustand
- **数据获取**: tRPC
- **编辑器**: @uiw/react-textarea-code-editor
- **动画库**: Framer Motion
- **图标库**: Font Awesome

### 后端技术栈
- **服务框架**: Next.js API Routes
- **API架构**: tRPC路由器
- **数据库ORM**: Prisma
- **主数据库**: PostgreSQL
- **用户认证**: NextAuth.js v5

## 系统架构设计

### 整体架构图

```mermaid
graph TB
    A[用户界面 - Next.js App Router] --> B[tRPC Client]
    B --> C[tRPC Server Router]
    C --> D[Prisma ORM]
    D --> E[PostgreSQL 数据库]
    
    F[NextAuth.js] --> A
    F --> G[Session 管理]
    
    H[Zustand Store] --> A
    I[Framer Motion] --> A
    J[daisyUI Components] --> A
```

### 目录结构

```
prompt-manager/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # 认证相关页面组
│   │   ├── login/
│   │   └── register/
│   ├── api/                      # API路由
│   │   ├── auth/                 # NextAuth配置
│   │   └── trpc/                 # tRPC处理器
│   ├── dashboard/                # 主应用页面
│   │   ├── page.tsx              # 主页面
│   │   ├── categories/           # 分类管理
│   │   ├── prompts/              # 提示词管理
│   │   └── settings/             # 设置页面
│   ├── components/               # 共享组件
│   ├── globals.css               # 全局样式
│   ├── layout.tsx                # 根布局
│   └── page.tsx                  # 首页
├── components/                   # 可复用组件
│   ├── ui/                       # 基础UI组件
│   ├── forms/                    # 表单组件
│   ├── modals/                   # 模态框组件
│   └── layout/                   # 布局组件
├── lib/                          # 工具库
│   ├── auth.ts                   # 认证配置
│   ├── db.ts                     # 数据库连接
│   ├── trpc/                     # tRPC配置
│   └── utils.ts                  # 工具函数
├── prisma/                       # Prisma配置
│   ├── schema.prisma             # 数据库模式
│   └── migrations/               # 数据库迁移
├── server/                       # 服务端代码
│   ├── api/                      # tRPC路由器
│   │   ├── routers/              # 路由模块
│   │   └── trpc.ts               # tRPC初始化
│   └── auth.ts                   # 服务端认证
├── stores/                       # Zustand状态管理
├── types/                        # TypeScript类型定义
└── utils/                        # 客户端工具函数
```

## 数据库设计

### 数据模型关系图

```mermaid
erDiagram
    User ||--o{ Prompt : creates
    User ||--o{ Category : creates
    User ||--o{ SearchHistory : has
    Category ||--o{ Prompt : contains
    Prompt ||--o{ PromptTag : has
    Tag ||--o{ PromptTag : belongs_to
    
    User {
        string id PK
        string email UK
        string name
        string image
        datetime emailVerified
        datetime createdAt
        datetime updatedAt
    }
    
    Category {
        string id PK
        string name
        string description
        string color
        string icon
        string userId FK
        datetime createdAt
        datetime updatedAt
    }
    
    Prompt {
        string id PK
        string title
        text content
        string description
        string categoryId FK
        string userId FK
        int usageCount
        datetime createdAt
        datetime updatedAt
    }
    
    Tag {
        string id PK
        string name
        string color
        datetime createdAt
    }
    
    PromptTag {
        string promptId FK
        string tagId FK
    }
    
    SearchHistory {
        string id PK
        string query
        string userId FK
        datetime createdAt
    }
```

### Prisma Schema

```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts       Account[]
  sessions       Session[]
  categories     Category[]
  prompts        Prompt[]
  searchHistory  SearchHistory[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Category {
  id          String   @id @default(cuid())
  name        String
  description String?
  color       String   @default("#3B82F6")
  icon        String   @default("folder")
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  prompts Prompt[]

  @@unique([name, userId])
}

model Prompt {
  id          String   @id @default(cuid())
  title       String
  content     String   @db.Text
  description String?
  categoryId  String?
  userId      String
  usageCount  Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user     User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  category Category?   @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  tags     PromptTag[]

  @@index([userId])
  @@index([categoryId])
}

model Tag {
  id        String   @id @default(cuid())
  name      String   @unique
  color     String   @default("#10B981")
  createdAt DateTime @default(now())

  prompts PromptTag[]
}

model PromptTag {
  promptId String
  tagId    String

  prompt Prompt @relation(fields: [promptId], references: [id], onDelete: Cascade)
  tag    Tag    @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([promptId, tagId])
}

model SearchHistory {
  id        String   @id @default(cuid())
  query     String
  userId    String
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}
```

## API设计

### tRPC路由器结构

```typescript
// server/api/root.ts
export const appRouter = createTRPCRouter({
  auth: authRouter,
  category: categoryRouter,
  prompt: promptRouter,
  tag: tagRouter,
  search: searchRouter,
  stats: statsRouter,
});

export type AppRouter = typeof appRouter;
```

### 主要API端点

#### 1. 认证路由 (authRouter)
```typescript
// 获取当前用户信息
getMe: protectedProcedure.query()

// 更新用户资料
updateProfile: protectedProcedure
  .input(updateProfileSchema)
  .mutation()
```

#### 2. 分类路由 (categoryRouter)
```typescript
// 获取用户所有分类
getAll: protectedProcedure.query()

// 创建分类
create: protectedProcedure
  .input(createCategorySchema)
  .mutation()

// 更新分类
update: protectedProcedure
  .input(updateCategorySchema)
  .mutation()

// 删除分类
delete: protectedProcedure
  .input(z.object({ id: z.string() }))
  .mutation()
```

#### 3. 提示词路由 (promptRouter)
```typescript
// 获取提示词列表（支持分页、筛选、搜索）
getAll: protectedProcedure
  .input(getPromptsSchema)
  .query()

// 根据ID获取提示词详情
getById: protectedProcedure
  .input(z.object({ id: z.string() }))
  .query()

// 创建提示词
create: protectedProcedure
  .input(createPromptSchema)
  .mutation()

// 更新提示词
update: protectedProcedure
  .input(updatePromptSchema)
  .mutation()

// 删除提示词
delete: protectedProcedure
  .input(z.object({ id: z.string() }))
  .mutation()

// 增加使用次数
incrementUsage: protectedProcedure
  .input(z.object({ id: z.string() }))
  .mutation()

// 批量导入
batchImport: protectedProcedure
  .input(batchImportSchema)
  .mutation()
```

## 组件设计

### 核心组件架构

#### 1. 布局组件
```typescript
// components/layout/DashboardLayout.tsx
interface DashboardLayoutProps {
  children: React.ReactNode;
}

// 包含侧边栏、顶部导航、主内容区域
```

#### 2. 提示词卡片组件
```typescript
// components/prompts/PromptCard.tsx
interface PromptCardProps {
  prompt: Prompt & {
    category?: Category;
    tags: (PromptTag & { tag: Tag })[];
  };
  onCopy: (content: string) => void;
  onEdit: (prompt: Prompt) => void;
  onDelete: (id: string) => void;
}
```

#### 3. 分类侧边栏组件
```typescript
// components/categories/CategorySidebar.tsx
interface CategorySidebarProps {
  categories: Category[];
  selectedCategoryId?: string;
  onCategorySelect: (categoryId: string | null) => void;
  onCategoryCreate: () => void;
  onCategoryEdit: (category: Category) => void;
  onCategoryDelete: (id: string) => void;
}
```

#### 4. 搜索组件
```typescript
// components/search/SearchBar.tsx
interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: (query: string) => void;
  placeholder?: string;
  showHistory?: boolean;
}
```

#### 5. 模态框组件
```typescript
// components/modals/PromptModal.tsx
interface PromptModalProps {
  isOpen: boolean;
  onClose: () => void;
  prompt?: Prompt;
  mode: 'create' | 'edit' | 'view';
}
```

## 状态管理设计

### Zustand Store结构

```typescript
// stores/useAppStore.ts
interface AppState {
  // UI状态
  sidebarCollapsed: boolean;
  currentView: 'grid' | 'list';
  
  // 筛选状态
  selectedCategoryId: string | null;
  searchQuery: string;
  selectedTags: string[];
  
  // 模态框状态
  promptModal: {
    isOpen: boolean;
    mode: 'create' | 'edit' | 'view';
    promptId?: string;
  };
  
  categoryModal: {
    isOpen: boolean;
    mode: 'create' | 'edit';
    categoryId?: string;
  };
  
  // Actions
  toggleSidebar: () => void;
  setCurrentView: (view: 'grid' | 'list') => void;
  setSelectedCategory: (categoryId: string | null) => void;
  setSearchQuery: (query: string) => void;
  toggleTag: (tagId: string) => void;
  openPromptModal: (mode: 'create' | 'edit' | 'view', promptId?: string) => void;
  closePromptModal: () => void;
  openCategoryModal: (mode: 'create' | 'edit', categoryId?: string) => void;
  closeCategoryModal: () => void;
}
```

## 用户界面设计

### 设计系统

#### 1. 颜色方案
```css
/* 基于daisyUI的颜色系统 */
:root {
  --primary: #3B82F6;      /* 蓝色 - 主要操作 */
  --secondary: #10B981;    /* 绿色 - 成功状态 */
  --accent: #F59E0B;       /* 橙色 - 强调色 */
  --neutral: #6B7280;      /* 灰色 - 中性色 */
  --base-100: #FFFFFF;     /* 白色 - 背景 */
  --base-200: #F9FAFB;     /* 浅灰 - 次要背景 */
  --base-300: #E5E7EB;     /* 中灰 - 边框 */
  --info: #3B82F6;         /* 信息色 */
  --success: #10B981;      /* 成功色 */
  --warning: #F59E0B;      /* 警告色 */
  --error: #EF4444;        /* 错误色 */
}
```

#### 2. 字体系统
```css
/* 中文字体优化 */
.font-sans {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 
               'WenQuanYi Micro Hei', sans-serif;
}

.font-mono {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 
               'Source Code Pro', monospace;
}
```

#### 3. 组件样式规范
- 卡片：使用`card`、`card-body`、`shadow-sm`等daisyUI类
- 按钮：统一使用`btn`基类，配合颜色和尺寸修饰符
- 输入框：使用`input`、`input-bordered`等类
- 模态框：使用`modal`、`modal-box`等类

### 响应式设计

#### 断点系统
```css
/* Tailwind CSS断点 */
sm: 640px   /* 小屏幕 */
md: 768px   /* 中等屏幕 */
lg: 1024px  /* 大屏幕 */
xl: 1280px  /* 超大屏幕 */
2xl: 1536px /* 超超大屏幕 */
```

#### 布局适配
- **移动端 (< 768px)**: 单列布局，侧边栏折叠为抽屉
- **平板端 (768px - 1024px)**: 两列布局，可折叠侧边栏
- **桌面端 (> 1024px)**: 三列布局，固定侧边栏

## 性能优化设计

### 1. 数据获取优化
- 使用tRPC的批量请求功能
- 实现无限滚动分页
- 智能预加载相关数据

### 2. 缓存策略
- React Query缓存API响应
- 本地存储用户偏好设置
- 浏览器缓存静态资源

### 3. 代码分割
- 路由级别的代码分割
- 组件懒加载
- 动态导入第三方库

### 4. 图片优化
- Next.js Image组件优化
- WebP格式支持
- 响应式图片

## 安全设计

### 1. 认证与授权
- NextAuth.js处理OAuth和邮箱登录
- JWT token管理
- 会话安全

### 2. 数据验证
- Zod schema验证输入
- CSRF保护
- XSS防护

### 3. 数据库安全
- Prisma ORM防SQL注入
- 行级安全策略
- 数据加密

## 错误处理设计

### 1. 客户端错误处理
```typescript
// 全局错误边界
class ErrorBoundary extends React.Component {
  // 捕获React组件错误
}

// tRPC错误处理
const utils = api.useContext();
const mutation = api.prompt.create.useMutation({
  onError: (error) => {
    toast.error(error.message);
  },
  onSuccess: () => {
    toast.success('提示词创建成功');
    utils.prompt.getAll.invalidate();
  },
});
```

### 2. 服务端错误处理
```typescript
// tRPC错误处理
import { TRPCError } from '@trpc/server';

if (!user) {
  throw new TRPCError({
    code: 'UNAUTHORIZED',
    message: '请先登录',
  });
}
```

## 测试策略

### 1. 单元测试
- 使用Jest + React Testing Library
- 测试组件渲染和交互
- 测试工具函数

### 2. 集成测试
- 测试API端点
- 测试数据库操作
- 测试认证流程

### 3. E2E测试
- 使用Playwright
- 测试关键用户流程
- 跨浏览器测试

## 部署设计

### 1. 开发环境
- 本地PostgreSQL数据库
- Next.js开发服务器
- 热重载支持

### 2. 生产环境
- Vercel部署Next.js应用
- Supabase或Railway托管PostgreSQL
- 环境变量管理

### 3. CI/CD流程
- GitHub Actions自动化
- 代码质量检查
- 自动化测试
- 自动部署

## 监控与分析

### 1. 性能监控
- Vercel Analytics
- Core Web Vitals监控
- 错误追踪

### 2. 用户分析
- 使用统计
- 功能使用频率
- 用户行为分析

这个设计文档涵盖了提示词管理工具的完整技术架构、数据库设计、API设计、组件设计、状态管理、用户界面、性能优化、安全性、错误处理、测试策略和部署方案。设计充分考虑了现代Web应用的最佳实践，确保系统的可扩展性、可维护性和用户体验。