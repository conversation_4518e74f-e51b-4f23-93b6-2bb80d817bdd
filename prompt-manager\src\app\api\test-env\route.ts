/**
 * 测试环境变量的API端点
 */

import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const databaseUrl = process.env.DATABASE_URL;
    const nodeEnv = process.env.NODE_ENV;
    
    console.log('环境变量检查:', {
      DATABASE_URL: databaseUrl,
      NODE_ENV: nodeEnv,
    });
    
    return NextResponse.json({
      success: true,
      data: {
        DATABASE_URL: databaseUrl,
        NODE_ENV: nodeEnv,
        hasEnvFile: !!databaseUrl,
      },
    });
  } catch (error) {
    console.error('环境变量检查失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
