/**
 * 搜索功能路由器
 * 处理搜索和搜索历史相关操作
 */

import { z } from 'zod';
import { createTRPCRouter, protectedProcedure } from '~/server/api/trpc';

export const searchRouter = createTRPCRouter({
  /**
   * 搜索提示词
   */
  prompts: protectedProcedure
    .input(
      z.object({
        query: z.string().min(1, '搜索关键词不能为空'),
        categoryId: z.string().optional(),
        tags: z.array(z.string()).optional(),
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(20),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { query, categoryId, tags, page, limit } = input;
      const skip = (page - 1) * limit;

      // 保存搜索历史
      await ctx.db.searchHistory.create({
        data: {
          query,
          userId: ctx.session.user.id,
        },
      });

      // 构建搜索条件
      const where: any = {
        userId: ctx.session.user.id,
        OR: [
          { title: { contains: query, mode: 'insensitive' } },
          { content: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
        ],
      };

      if (categoryId) {
        where.categoryId = categoryId;
      }

      if (tags && tags.length > 0) {
        where.tags = {
          some: {
            tag: {
              name: { in: tags },
            },
          },
        };
      }

      // 获取总数
      const total = await ctx.db.prompt.count({ where });

      // 获取搜索结果
      const prompts = await ctx.db.prompt.findMany({
        where,
        include: {
          category: true,
          tags: {
            include: {
              tag: true,
            },
          },
        },
        orderBy: [
          { usageCount: 'desc' }, // 优先显示使用频率高的
          { updatedAt: 'desc' },
        ],
        skip,
        take: limit,
      });

      return {
        prompts,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  /**
   * 获取搜索历史
   */
  getHistory: protectedProcedure
    .input(z.object({ limit: z.number().min(1).max(50).default(10) }))
    .query(async ({ ctx, input }) => {
      return ctx.db.searchHistory.findMany({
        where: {
          userId: ctx.session.user.id,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: input.limit,
        distinct: ['query'], // 去重
      });
    }),

  /**
   * 清除搜索历史
   */
  clearHistory: protectedProcedure.mutation(async ({ ctx }) => {
    return ctx.db.searchHistory.deleteMany({
      where: {
        userId: ctx.session.user.id,
      },
    });
  }),

  /**
   * 删除特定搜索历史
   */
  deleteHistory: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 验证搜索历史所有权
      const searchHistory = await ctx.db.searchHistory.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
      });

      if (!searchHistory) {
        throw new Error('搜索历史不存在');
      }

      return ctx.db.searchHistory.delete({
        where: { id: input.id },
      });
    }),

  /**
   * 获取搜索建议
   */
  getSuggestions: protectedProcedure
    .input(z.object({ query: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      const { query } = input;

      // 搜索匹配的提示词标题
      const titleSuggestions = await ctx.db.prompt.findMany({
        where: {
          userId: ctx.session.user.id,
          title: {
            contains: query,
            mode: 'insensitive',
          },
        },
        select: {
          title: true,
        },
        take: 5,
        orderBy: {
          usageCount: 'desc',
        },
      });

      // 搜索匹配的标签
      const tagSuggestions = await ctx.db.tag.findMany({
        where: {
          name: {
            contains: query,
            mode: 'insensitive',
          },
          prompts: {
            some: {
              prompt: {
                userId: ctx.session.user.id,
              },
            },
          },
        },
        select: {
          name: true,
        },
        take: 5,
        orderBy: {
          name: 'asc',
        },
      });

      return {
        titles: titleSuggestions.map(p => p.title),
        tags: tagSuggestions.map(t => t.name),
      };
    }),
});
