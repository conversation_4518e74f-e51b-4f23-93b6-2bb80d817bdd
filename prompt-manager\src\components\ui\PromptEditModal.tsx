/**
 * 提示词编辑模态框组件
 * 用于创建和编辑提示词
 */

'use client';

import { useState, useEffect } from 'react';
import Modal from './Modal';

interface Tag {
  id: string;
  name: string;
  color: string;
}

interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
}

interface Prompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: Category;
  tags: Tag[];
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

interface PromptEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  prompt?: Prompt | null;
  categories?: Category[];
  availableTags?: Tag[];
  onSave: (promptData: {
    title: string;
    content: string;
    description?: string;
    categoryId?: string;
    tags: string[];
  }) => void;
}

export default function PromptEditModal({
  isOpen,
  onClose,
  prompt,
  categories = [],
  availableTags = [],
  onSave,
}: PromptEditModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    description: '',
    categoryId: '',
    tags: [] as string[],
  });
  const [newTagName, setNewTagName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 模拟数据
  const mockCategories: Category[] = [
    { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️' },
    { id: '2', name: '代码生成', color: '#10B981', icon: '💻' },
    { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐' },
  ];

  const mockTags: Tag[] = [
    { id: '1', name: 'AI', color: '#3B82F6' },
    { id: '2', name: '编程', color: '#10B981' },
    { id: '3', name: '创意', color: '#F59E0B' },
    { id: '4', name: '商务', color: '#EF4444' },
  ];

  const displayCategories = categories.length > 0 ? categories : mockCategories;
  const displayTags = availableTags.length > 0 ? availableTags : mockTags;

  // 初始化表单数据
  useEffect(() => {
    if (prompt) {
      setFormData({
        title: prompt.title,
        content: prompt.content,
        description: prompt.description || '',
        categoryId: prompt.category?.id || '',
        tags: prompt.tags.map(tag => tag.id),
      });
    } else {
      setFormData({
        title: '',
        content: '',
        description: '',
        categoryId: '',
        tags: [],
      });
    }
    setErrors({});
  }, [prompt, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '标题不能为空';
    } else if (formData.title.length > 200) {
      newErrors.title = '标题不能超过200个字符';
    }

    if (!formData.content.trim()) {
      newErrors.content = '内容不能为空';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = '描述不能超过500个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSave({
        title: formData.title.trim(),
        content: formData.content.trim(),
        description: formData.description.trim() || undefined,
        categoryId: formData.categoryId || undefined,
        tags: formData.tags,
      });
      onClose();
    } catch (error) {
      console.error('保存失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTagToggle = (tagId: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.includes(tagId)
        ? prev.tags.filter(id => id !== tagId)
        : [...prev.tags, tagId],
    }));
  };

  const handleAddNewTag = () => {
    if (newTagName.trim()) {
      // 这里应该调用API创建新标签，暂时模拟
      const newTagId = `new-${Date.now()}`;
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTagId],
      }));
      setNewTagName('');
    }
  };

  const isEditing = !!prompt;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={isEditing ? '编辑提示词' : '新建提示词'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* 标题 */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
            标题 *
          </label>
          <input
            type="text"
            id="title"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${
              errors.title ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="输入提示词标题"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title}</p>
          )}
        </div>

        {/* 描述 */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            描述
          </label>
          <textarea
            id="description"
            rows={2}
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${
              errors.description ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="简要描述这个提示词的用途"
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600">{errors.description}</p>
          )}
        </div>

        {/* 分类 */}
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
            分类
          </label>
          <select
            id="category"
            value={formData.categoryId}
            onChange={(e) => setFormData(prev => ({ ...prev, categoryId: e.target.value }))}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">选择分类</option>
            {displayCategories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.icon} {category.name}
              </option>
            ))}
          </select>
        </div>

        {/* 标签 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">标签</label>
          <div className="space-y-3">
            {/* 现有标签 */}
            <div className="flex flex-wrap gap-2">
              {displayTags.map((tag) => (
                <label key={tag.id} className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.tags.includes(tag.id)}
                    onChange={() => handleTagToggle(tag.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span
                    className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white"
                    style={{ backgroundColor: tag.color }}
                  >
                    {tag.name}
                  </span>
                </label>
              ))}
            </div>

            {/* 添加新标签 */}
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={newTagName}
                onChange={(e) => setNewTagName(e.target.value)}
                placeholder="添加新标签"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddNewTag();
                  }
                }}
              />
              <button
                type="button"
                onClick={handleAddNewTag}
                className="px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                添加
              </button>
            </div>
          </div>
        </div>

        {/* 内容 */}
        <div>
          <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
            提示词内容 *
          </label>
          <textarea
            id="content"
            rows={8}
            value={formData.content}
            onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
            className={`block w-full px-3 py-2 border rounded-md shadow-sm font-mono text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${
              errors.content ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="输入提示词内容..."
          />
          {errors.content && (
            <p className="mt-1 text-sm text-red-600">{errors.content}</p>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            取消
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? '保存中...' : (isEditing ? '保存更改' : '创建提示词')}
          </button>
        </div>
      </form>
    </Modal>
  );
}
