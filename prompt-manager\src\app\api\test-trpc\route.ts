/**
 * 测试tRPC调用的API端点
 */

import { NextRequest, NextResponse } from 'next/server';
import { appRouter } from '~/server/api/root';
import { createTRPCContext } from '~/server/api/trpc';

export async function GET(request: NextRequest) {
  try {
    console.log('测试tRPC调用...');
    
    // 创建tRPC上下文
    const ctx = await createTRPCContext({
      req: request as any,
      res: {} as any,
    });
    
    console.log('tRPC上下文创建成功:', {
      hasDb: !!ctx.db,
      hasSession: !!ctx.session,
      sessionUser: ctx.session?.user,
    });
    
    // 调用提示词API
    const caller = appRouter.createCaller(ctx);
    const result = await caller.prompt.getAll({
      page: 1,
      limit: 20,
      sortBy: 'updatedAt',
      sortOrder: 'desc',
    });
    
    console.log('tRPC调用成功:', result);
    
    return NextResponse.json({
      success: true,
      message: 'tRPC调用成功',
      data: result,
    });
  } catch (error) {
    console.error('tRPC调用失败:', error);
    
    return NextResponse.json({
      success: false,
      message: 'tRPC调用失败',
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
