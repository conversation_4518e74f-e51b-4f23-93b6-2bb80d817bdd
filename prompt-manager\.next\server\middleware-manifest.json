{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "G0rvZwDjitE6aMxnjTLk6LX63W71j0ht0ONrGDcB0pc=", "__NEXT_PREVIEW_MODE_ID": "0f9962ac541125fba81e3d4a92b13512", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fd1ca420829424e85a46770ac91b856d9782583e46e29df38baf94cd32e5e701", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "67f3630a65368b58330f46edcf1487d7e7227739dc6727eeee5b66ad7ad57422"}}}, "functions": {}, "sortedMiddleware": ["/"]}