/**
 * 提示词卡片组件
 * 显示提示词的基本信息和操作按钮
 */

'use client';

import { useState } from 'react';
import { SmartHighlightText } from './HighlightText';

interface Tag {
  id: string;
  name: string;
  color: string;
}

interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
}

interface Prompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: Category;
  tags: Tag[];
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

interface PromptCardProps {
  prompt: Prompt;
  searchQuery?: string;
  onEdit?: (prompt: Prompt) => void;
  onDelete?: (promptId: string) => void;
  onCopy?: (content: string) => void;
  onView?: (prompt: Prompt) => void;
}

export default function PromptCard({
  prompt,
  searchQuery = '',
  onEdit,
  onDelete,
  onCopy,
  onView,
}: PromptCardProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(prompt.content);
      onCopy?.(prompt.content);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  const handleMenuToggle = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleEdit = () => {
    onEdit?.(prompt);
    setIsMenuOpen(false);
  };

  const handleDelete = () => {
    if (window.confirm('确定要删除这个提示词吗？')) {
      onDelete?.(prompt.id);
    }
    setIsMenuOpen(false);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
      {/* 卡片头部 */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 truncate">
              <SmartHighlightText
                text={prompt.title}
                searchQuery={searchQuery}
                highlightClassName="bg-yellow-200 text-yellow-900 px-1 rounded"
              />
            </h3>
            {prompt.description && (
              <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                <SmartHighlightText
                  text={prompt.description}
                  searchQuery={searchQuery}
                  highlightClassName="bg-yellow-200 text-yellow-900 px-1 rounded"
                  maxLength={100}
                />
              </p>
            )}
          </div>
          
          {/* 操作菜单 */}
          <div className="relative ml-4">
            <button
              onClick={handleMenuToggle}
              className="p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
              </svg>
            </button>
            
            {isMenuOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                <div className="py-1">
                  <button
                    onClick={() => onView?.(prompt)}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    查看详情
                  </button>
                  <button
                    onClick={handleEdit}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    编辑
                  </button>
                  <button
                    onClick={handleCopy}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    复制内容
                  </button>
                  <button
                    onClick={handleDelete}
                    className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    删除
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 分类和标签 */}
        <div className="mt-3 flex items-center space-x-2">
          {prompt.category && (
            <span
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white"
              style={{ backgroundColor: prompt.category.color }}
            >
              <span className="mr-1">{prompt.category.icon}</span>
              {prompt.category.name}
            </span>
          )}
          
          {prompt.tags.slice(0, 3).map((tag) => (
            <span
              key={tag.id}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white"
              style={{ backgroundColor: tag.color }}
            >
              {tag.name}
            </span>
          ))}
          
          {prompt.tags.length > 3 && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
              +{prompt.tags.length - 3}
            </span>
          )}
        </div>
      </div>

      {/* 卡片内容 */}
      <div className="p-4">
        <div className="text-sm text-gray-700 whitespace-pre-wrap">
          {truncateContent(prompt.content)}
        </div>
      </div>

      {/* 卡片底部 */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-100 flex items-center justify-between">
        <div className="flex items-center space-x-4 text-xs text-gray-500">
          <span>使用 {prompt.usageCount} 次</span>
          <span>更新于 {formatDate(prompt.updatedAt)}</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleCopy}
            className={`inline-flex items-center px-3 py-1 rounded-md text-xs font-medium transition-colors ${
              isCopied
                ? 'bg-green-100 text-green-800'
                : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
            }`}
          >
            {isCopied ? (
              <>
                <svg className="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                已复制
              </>
            ) : (
              <>
                <svg className="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                复制
              </>
            )}
          </button>
          
          <button
            onClick={() => onView?.(prompt)}
            className="inline-flex items-center px-3 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
          >
            查看
          </button>
        </div>
      </div>
    </div>
  );
}
