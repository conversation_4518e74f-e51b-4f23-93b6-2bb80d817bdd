{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CTRPCProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastContainer.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=false!"]}