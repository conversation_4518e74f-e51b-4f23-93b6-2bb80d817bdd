/**
 * 统计面板组件
 * 显示提示词使用统计信息
 */

'use client';

import { useState } from 'react';

interface StatsData {
  totalPrompts: number;
  totalCategories: number;
  totalTags: number;
  totalUsage: number;
  recentActivity: {
    date: string;
    count: number;
  }[];
  topCategories: {
    name: string;
    count: number;
    color: string;
  }[];
  topPrompts: {
    id: string;
    title: string;
    usageCount: number;
  }[];
}

interface StatsPanelProps {
  data?: StatsData;
}

export default function StatsPanel({ data }: StatsPanelProps) {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');

  // 模拟数据
  const mockData: StatsData = {
    totalPrompts: 156,
    totalCategories: 8,
    totalTags: 24,
    totalUsage: 1247,
    recentActivity: [
      { date: '2024-01-20', count: 15 },
      { date: '2024-01-19', count: 23 },
      { date: '2024-01-18', count: 18 },
      { date: '2024-01-17', count: 12 },
      { date: '2024-01-16', count: 28 },
      { date: '2024-01-15', count: 19 },
      { date: '2024-01-14', count: 22 },
    ],
    topCategories: [
      { name: '写作助手', count: 45, color: '#3B82F6' },
      { name: '代码生成', count: 32, color: '#10B981' },
      { name: '翻译工具', count: 28, color: '#F59E0B' },
      { name: '数据分析', count: 21, color: '#EF4444' },
      { name: '创意设计', count: 18, color: '#8B5CF6' },
    ],
    topPrompts: [
      { id: '1', title: '写作助手 - 文章大纲生成', usageCount: 89 },
      { id: '2', title: '代码生成 - React组件模板', usageCount: 67 },
      { id: '3', title: '翻译工具 - 专业文档翻译', usageCount: 54 },
      { id: '4', title: 'SQL查询生成器', usageCount: 43 },
      { id: '5', title: '邮件模板生成', usageCount: 38 },
    ],
  };

  const displayData = data || mockData;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
  };

  const getMaxActivity = () => {
    return Math.max(...displayData.recentActivity.map(item => item.count));
  };

  return (
    <div className="space-y-6">
      {/* 概览统计 */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总提示词</p>
              <p className="text-2xl font-semibold text-gray-900">{displayData.totalPrompts}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-7l2 2-2 2m2-2H9m10 7l2 2-2 2m2-2H9" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">分类数量</p>
              <p className="text-2xl font-semibold text-gray-900">{displayData.totalCategories}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">标签数量</p>
              <p className="text-2xl font-semibold text-gray-900">{displayData.totalTags}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总使用次数</p>
              <p className="text-2xl font-semibold text-gray-900">{displayData.totalUsage}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 活动趋势 */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">使用趋势</h3>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as '7d' | '30d' | '90d')}
            className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
            <option value="90d">最近90天</option>
          </select>
        </div>
        
        <div className="flex items-end space-x-2 h-32">
          {displayData.recentActivity.map((item, index) => (
            <div key={index} className="flex-1 flex flex-col items-center">
              <div
                className="w-full bg-blue-500 rounded-t"
                style={{
                  height: `${(item.count / getMaxActivity()) * 100}%`,
                  minHeight: '4px',
                }}
              />
              <div className="mt-2 text-xs text-gray-500 text-center">
                {formatDate(item.date)}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 热门分类 */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">热门分类</h3>
          <div className="space-y-3">
            {displayData.topCategories.map((category, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div
                    className="w-3 h-3 rounded-full mr-3"
                    style={{ backgroundColor: category.color }}
                  />
                  <span className="text-sm font-medium text-gray-900">{category.name}</span>
                </div>
                <span className="text-sm text-gray-500">{category.count} 个</span>
              </div>
            ))}
          </div>
        </div>

        {/* 热门提示词 */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">热门提示词</h3>
          <div className="space-y-3">
            {displayData.topPrompts.map((prompt, index) => (
              <div key={prompt.id} className="flex items-center justify-between">
                <div className="flex items-center min-w-0 flex-1">
                  <span className="text-sm font-medium text-gray-500 mr-3">#{index + 1}</span>
                  <span className="text-sm font-medium text-gray-900 truncate">{prompt.title}</span>
                </div>
                <span className="text-sm text-gray-500 ml-2">{prompt.usageCount} 次</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
