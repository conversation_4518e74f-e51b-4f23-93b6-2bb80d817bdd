/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cerror%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CTRPCProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastContainer.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccontexts%5C%5CErrorContext.tsx%22%2C%22ids%22%3A%5B%22ErrorProvider%22%5D%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cerror%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CTRPCProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastContainer.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccontexts%5C%5CErrorContext.tsx%22%2C%22ids%22%3A%5B%22ErrorProvider%22%5D%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/SessionProvider.tsx */ \"(app-pages-browser)/./src/components/auth/SessionProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/error/ErrorBoundary.tsx */ \"(app-pages-browser)/./src/components/error/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/TRPCProvider.tsx */ \"(app-pages-browser)/./src/components/providers/TRPCProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/ToastContainer.tsx */ \"(app-pages-browser)/./src/components/ui/ToastContainer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ErrorContext.tsx */ \"(app-pages-browser)/./src/contexts/ErrorContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cerror%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CTRPCProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastContainer.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCursor%20Project%5C%5CAugment%5C%5Cprompt-manager%5C%5Csrc%5C%5Ccontexts%5C%5CErrorContext.tsx%22%2C%22ids%22%3A%5B%22ErrorProvider%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d5d3786640ca\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcQ3Vyc29yIFByb2plY3RcXEF1Z21lbnRcXHByb21wdC1tYW5hZ2VyXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkNWQzNzg2NjQwY2FcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/error/ErrorBoundary.tsx":
/*!************************************************!*\
  !*** ./src/components/error/ErrorBoundary.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorDisplay: () => (/* binding */ ErrorDisplay),\n/* harmony export */   \"default\": () => (/* binding */ ErrorBoundary),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   isAuthError: () => (/* binding */ isAuthError),\n/* harmony export */   isNetworkError: () => (/* binding */ isNetworkError),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * 错误边界组件\n * 捕获React组件树中的JavaScript错误，记录错误并显示备用UI\n */ /* __next_internal_client_entry_do_not_use__ default,useErrorHandler,ErrorDisplay,getErrorMessage,isNetworkError,isAuthError auto */ \nvar _s = $RefreshSig$();\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError(error) {\n        // 更新state，下次渲染将显示错误UI\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        // 记录错误信息\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n        this.setState({\n            error,\n            errorInfo\n        });\n        // 调用外部错误处理函数\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n        // 在生产环境中，可以将错误发送到错误报告服务\n        if (false) {}\n    }\n    render() {\n        if (this.state.hasError) {\n            var _this_state_errorInfo;\n            // 如果提供了自定义fallback，使用它\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            // 默认错误UI\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full space-y-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto h-24 w-24 text-red-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1,\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"mt-6 text-3xl font-extrabold text-gray-900\",\n                                children: \"出现了一些问题\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-gray-600\",\n                                children: \"应用遇到了意外错误，我们正在努力修复这个问题。\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, this),\n                             true && this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-4 bg-red-50 border border-red-200 rounded-md text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-red-800 mb-2\",\n                                        children: \"错误详情：\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"text-xs text-red-700 whitespace-pre-wrap overflow-auto max-h-40\",\n                                        children: [\n                                            this.state.error.toString(),\n                                            (_this_state_errorInfo = this.state.errorInfo) === null || _this_state_errorInfo === void 0 ? void 0 : _this_state_errorInfo.componentStack\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: this.handleRetry,\n                                        className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: \"重试\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.reload(),\n                                        className: \"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: \"刷新页面\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: \"返回首页\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props), this.handleRetry = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined,\n                errorInfo: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n}\n\n/**\n * 简化的错误边界Hook版本\n * 用于函数组件中的错误处理\n */ function useErrorHandler() {\n    _s();\n    const [error, setError] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(null);\n    const resetError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useErrorHandler.useCallback[resetError]\": ()=>{\n            setError(null);\n        }\n    }[\"useErrorHandler.useCallback[resetError]\"], []);\n    const captureError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useErrorHandler.useCallback[captureError]\": (error)=>{\n            console.error('Error captured:', error);\n            setError(error);\n        }\n    }[\"useErrorHandler.useCallback[captureError]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"useErrorHandler.useEffect\": ()=>{\n            if (error) {\n                // 在生产环境中发送错误报告\n                if (false) {}\n            }\n        }\n    }[\"useErrorHandler.useEffect\"], [\n        error\n    ]);\n    return {\n        error,\n        resetError,\n        captureError\n    };\n}\n_s(useErrorHandler, \"6/vI1P7j4DTuf/iYIWOmREoznl4=\");\nfunction ErrorDisplay(param) {\n    let { error, onRetry, onDismiss, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-md p-4 \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-5 w-5 text-red-400\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-3 flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-red-800\",\n                            children: \"出现错误\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 text-sm text-red-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: error.message || '发生了未知错误'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        (onRetry || onDismiss) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex space-x-2\",\n                            children: [\n                                onRetry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onRetry,\n                                    className: \"text-sm bg-red-100 text-red-800 rounded-md px-2 py-1 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500\",\n                                    children: \"重试\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, this),\n                                onDismiss && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onDismiss,\n                                    className: \"text-sm text-red-600 hover:text-red-500 focus:outline-none focus:ring-2 focus:ring-red-500\",\n                                    children: \"忽略\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                onDismiss && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-auto pl-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"-mx-1.5 -my-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onDismiss,\n                            className: \"inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"关闭\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n_c = ErrorDisplay;\n/**\n * API错误处理工具函数\n */ function getErrorMessage(error) {\n    if (error instanceof Error) {\n        return error.message;\n    }\n    if (typeof error === 'string') {\n        return error;\n    }\n    if (error && typeof error === 'object' && 'message' in error) {\n        return String(error.message);\n    }\n    return '发生了未知错误';\n}\n/**\n * 网络错误检测\n */ function isNetworkError(error) {\n    if (error instanceof Error) {\n        return error.message.includes('fetch') || error.message.includes('network') || error.message.includes('Failed to fetch');\n    }\n    return false;\n}\n/**\n * 认证错误检测\n */ function isAuthError(error) {\n    if (error instanceof Error) {\n        return error.message.includes('UNAUTHORIZED') || error.message.includes('401') || error.message.includes('Authentication');\n    }\n    return false;\n}\nvar _c;\n$RefreshReg$(_c, \"ErrorDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/error/ErrorBoundary.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ErrorContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ErrorContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorProvider: () => (/* binding */ ErrorProvider),\n/* harmony export */   useAsyncErrorHandler: () => (/* binding */ useAsyncErrorHandler),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler),\n/* harmony export */   useFormErrorHandler: () => (/* binding */ useFormErrorHandler),\n/* harmony export */   useRetry: () => (/* binding */ useRetry),\n/* harmony export */   withErrorHandler: () => (/* binding */ withErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_ToastContainer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ToastContainer */ \"(app-pages-browser)/./src/components/ui/ToastContainer.tsx\");\n/* harmony import */ var _components_error_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/error/ErrorBoundary */ \"(app-pages-browser)/./src/components/error/ErrorBoundary.tsx\");\n/**\n * 全局错误处理Context\n * 提供统一的错误处理和用户通知机制\n */ /* __next_internal_client_entry_do_not_use__ ErrorProvider,useErrorHandler,withErrorHandler,useAsyncErrorHandler,useFormErrorHandler,useRetry auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$();\n\n\n\nconst ErrorContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ErrorProvider(param) {\n    let { children } = param;\n    _s();\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { showError, showWarning, showInfo } = (0,_components_ui_ToastContainer__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const generateId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ErrorProvider.useCallback[generateId]\": ()=>{\n            return Date.now().toString(36) + Math.random().toString(36).substr(2);\n        }\n    }[\"ErrorProvider.useCallback[generateId]\"], []);\n    const reportError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ErrorProvider.useCallback[reportError]\": (error, context)=>{\n            const errorInfo = {\n                id: generateId(),\n                error,\n                timestamp: new Date(),\n                context,\n                handled: false\n            };\n            setErrors({\n                \"ErrorProvider.useCallback[reportError]\": (prev)=>[\n                        ...prev,\n                        errorInfo\n                    ]\n            }[\"ErrorProvider.useCallback[reportError]\"]);\n            // 记录到控制台\n            console.error('Error reported:', error, {\n                context,\n                timestamp: errorInfo.timestamp\n            });\n            // 在生产环境中发送到错误报告服务\n            if (false) {}\n            return errorInfo.id;\n        }\n    }[\"ErrorProvider.useCallback[reportError]\"], [\n        generateId\n    ]);\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ErrorProvider.useCallback[clearError]\": (id)=>{\n            setErrors({\n                \"ErrorProvider.useCallback[clearError]\": (prev)=>prev.filter({\n                        \"ErrorProvider.useCallback[clearError]\": (error)=>error.id !== id\n                    }[\"ErrorProvider.useCallback[clearError]\"])\n            }[\"ErrorProvider.useCallback[clearError]\"]);\n        }\n    }[\"ErrorProvider.useCallback[clearError]\"], []);\n    const clearAllErrors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ErrorProvider.useCallback[clearAllErrors]\": ()=>{\n            setErrors([]);\n        }\n    }[\"ErrorProvider.useCallback[clearAllErrors]\"], []);\n    const handleApiError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ErrorProvider.useCallback[handleApiError]\": (error, context)=>{\n            const errorMessage = (0,_components_error_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__.getErrorMessage)(error);\n            const errorObj = error instanceof Error ? error : new Error(errorMessage);\n            // 根据错误类型显示不同的提示\n            if ((0,_components_error_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                showWarning('认证失败', '请重新登录后再试');\n            // 可以在这里触发重定向到登录页面\n            // router.push('/auth/signin');\n            } else if ((0,_components_error_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__.isNetworkError)(error)) {\n                showError('网络错误', '请检查网络连接后重试');\n            } else if (errorMessage.includes('FORBIDDEN')) {\n                showError('权限不足', '您没有权限执行此操作');\n            } else if (errorMessage.includes('NOT_FOUND')) {\n                showError('资源不存在', '请求的资源未找到');\n            } else if (errorMessage.includes('CONFLICT')) {\n                showError('操作冲突', '资源已被其他用户修改，请刷新后重试');\n            } else if (errorMessage.includes('VALIDATION_ERROR')) {\n                showError('数据验证失败', '请检查输入的数据格式');\n            } else {\n                showError('操作失败', errorMessage);\n            }\n            reportError(errorObj, context);\n        }\n    }[\"ErrorProvider.useCallback[handleApiError]\"], [\n        showError,\n        showWarning,\n        reportError\n    ]);\n    const handleNetworkError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ErrorProvider.useCallback[handleNetworkError]\": (error, context)=>{\n            showError('网络连接失败', '请检查网络连接后重试');\n            reportError(error, context);\n        }\n    }[\"ErrorProvider.useCallback[handleNetworkError]\"], [\n        showError,\n        reportError\n    ]);\n    const handleAuthError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ErrorProvider.useCallback[handleAuthError]\": (error, context)=>{\n            showWarning('登录已过期', '请重新登录');\n            reportError(error, context);\n            // 清除本地存储的认证信息\n            localStorage.removeItem('prompt-manager-storage');\n            // 重定向到登录页面\n            setTimeout({\n                \"ErrorProvider.useCallback[handleAuthError]\": ()=>{\n                    window.location.href = '/auth/signin';\n                }\n            }[\"ErrorProvider.useCallback[handleAuthError]\"], 2000);\n        }\n    }[\"ErrorProvider.useCallback[handleAuthError]\"], [\n        showWarning,\n        reportError\n    ]);\n    const value = {\n        errors,\n        reportError,\n        clearError,\n        clearAllErrors,\n        handleApiError,\n        handleNetworkError,\n        handleAuthError\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\contexts\\\\ErrorContext.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s(ErrorProvider, \"PRqPqsfD8ogez8fRHn7gJILrA94=\", false, function() {\n    return [\n        _components_ui_ToastContainer__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = ErrorProvider;\nfunction useErrorHandler() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ErrorContext);\n    if (context === undefined) {\n        throw new Error('useErrorHandler must be used within an ErrorProvider');\n    }\n    return context;\n}\n_s1(useErrorHandler, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n/**\n * 高阶组件：为组件添加错误处理能力\n */ function withErrorHandler(Component, defaultContext) {\n    var _s = $RefreshSig$();\n    return _s(function WrappedComponent(props) {\n        _s();\n        const { handleApiError } = useErrorHandler();\n        const enhancedProps = {\n            ...props,\n            onError: (error)=>{\n                handleApiError(error, defaultContext);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...enhancedProps\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\Augment\\\\prompt-manager\\\\src\\\\contexts\\\\ErrorContext.tsx\",\n            lineNumber: 161,\n            columnNumber: 12\n        }, this);\n    }, \"hV4dszhlgMmckLtdl0HZ5g2xS1Q=\", false, function() {\n        return [\n            useErrorHandler\n        ];\n    });\n}\n/**\n * Hook：为异步操作添加错误处理\n */ function useAsyncErrorHandler() {\n    _s2();\n    const { handleApiError } = useErrorHandler();\n    const executeWithErrorHandling = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useAsyncErrorHandler.useCallback[executeWithErrorHandling]\": async (asyncFn, context, customErrorHandler)=>{\n            try {\n                return await asyncFn();\n            } catch (error) {\n                if (customErrorHandler) {\n                    customErrorHandler(error);\n                } else {\n                    handleApiError(error, context);\n                }\n                return null;\n            }\n        }\n    }[\"useAsyncErrorHandler.useCallback[executeWithErrorHandling]\"], [\n        handleApiError\n    ]);\n    return {\n        executeWithErrorHandling\n    };\n}\n_s2(useAsyncErrorHandler, \"mQF6n3dUspl2rjgbQs+h20t9FEw=\", false, function() {\n    return [\n        useErrorHandler\n    ];\n});\n/**\n * Hook：为表单提交添加错误处理\n */ function useFormErrorHandler() {\n    _s3();\n    const { handleApiError } = useErrorHandler();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitError, setSubmitError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useFormErrorHandler.useCallback[handleSubmit]\": async (submitFn, options)=>{\n            setIsSubmitting(true);\n            setSubmitError(null);\n            try {\n                const result = await submitFn();\n                if (options === null || options === void 0 ? void 0 : options.onSuccess) {\n                    options.onSuccess(result);\n                }\n                return result;\n            } catch (error) {\n                const errorMessage = (0,_components_error_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__.getErrorMessage)(error);\n                setSubmitError(errorMessage);\n                if (options === null || options === void 0 ? void 0 : options.onError) {\n                    options.onError(error);\n                } else {\n                    handleApiError(error, options === null || options === void 0 ? void 0 : options.context);\n                }\n                return null;\n            } finally{\n                setIsSubmitting(false);\n            }\n        }\n    }[\"useFormErrorHandler.useCallback[handleSubmit]\"], [\n        handleApiError\n    ]);\n    const clearSubmitError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useFormErrorHandler.useCallback[clearSubmitError]\": ()=>{\n            setSubmitError(null);\n        }\n    }[\"useFormErrorHandler.useCallback[clearSubmitError]\"], []);\n    return {\n        isSubmitting,\n        submitError,\n        handleSubmit,\n        clearSubmitError\n    };\n}\n_s3(useFormErrorHandler, \"whsPWcSR7Xsdck3OodZ4TpqAxUU=\", false, function() {\n    return [\n        useErrorHandler\n    ];\n});\n/**\n * 错误重试Hook\n */ function useRetry() {\n    _s4();\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const retry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useRetry.useCallback[retry]\": async function(asyncFn) {\n            let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n            setIsRetrying(true);\n            for(let i = 0; i <= maxRetries; i++){\n                try {\n                    const result = await asyncFn();\n                    setRetryCount(0);\n                    setIsRetrying(false);\n                    return result;\n                } catch (error) {\n                    setRetryCount(i + 1);\n                    if (i === maxRetries) {\n                        setIsRetrying(false);\n                        throw error;\n                    }\n                    // 等待指定时间后重试\n                    await new Promise({\n                        \"useRetry.useCallback[retry]\": (resolve)=>setTimeout(resolve, delay * Math.pow(2, i))\n                    }[\"useRetry.useCallback[retry]\"]);\n                }\n            }\n            setIsRetrying(false);\n            return null;\n        }\n    }[\"useRetry.useCallback[retry]\"], []);\n    const resetRetry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useRetry.useCallback[resetRetry]\": ()=>{\n            setRetryCount(0);\n            setIsRetrying(false);\n        }\n    }[\"useRetry.useCallback[resetRetry]\"], []);\n    return {\n        retry,\n        retryCount,\n        isRetrying,\n        resetRetry\n    };\n}\n_s4(useRetry, \"RdQK1P6/eCFC1q95NT3B6SBYlao=\");\nvar _c;\n$RefreshReg$(_c, \"ErrorProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ErrorContext.tsx\n"));

/***/ })

});