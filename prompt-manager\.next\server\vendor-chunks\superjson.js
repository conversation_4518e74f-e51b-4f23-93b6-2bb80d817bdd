"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/superjson";
exports.ids = ["vendor-chunks/superjson"];
exports.modules = {

/***/ "(ssr)/./node_modules/superjson/dist/accessDeep.js":
/*!***************************************************!*\
  !*** ./node_modules/superjson/dist/accessDeep.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDeep: () => (/* binding */ getDeep),\n/* harmony export */   setDeep: () => (/* binding */ setDeep)\n/* harmony export */ });\n/* harmony import */ var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is.js */ \"(ssr)/./node_modules/superjson/dist/is.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/superjson/dist/util.js\");\n\n\nconst getNthKey = (value, n) => {\n    if (n > value.size)\n        throw new Error('index out of bounds');\n    const keys = value.keys();\n    while (n > 0) {\n        keys.next();\n        n--;\n    }\n    return keys.next().value;\n};\nfunction validatePath(path) {\n    if ((0,_util_js__WEBPACK_IMPORTED_MODULE_1__.includes)(path, '__proto__')) {\n        throw new Error('__proto__ is not allowed as a property');\n    }\n    if ((0,_util_js__WEBPACK_IMPORTED_MODULE_1__.includes)(path, 'prototype')) {\n        throw new Error('prototype is not allowed as a property');\n    }\n    if ((0,_util_js__WEBPACK_IMPORTED_MODULE_1__.includes)(path, 'constructor')) {\n        throw new Error('constructor is not allowed as a property');\n    }\n}\nconst getDeep = (object, path) => {\n    validatePath(path);\n    for (let i = 0; i < path.length; i++) {\n        const key = path[i];\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet)(object)) {\n            object = getNthKey(object, +key);\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap)(object)) {\n            const row = +key;\n            const type = +path[++i] === 0 ? 'key' : 'value';\n            const keyOfRow = getNthKey(object, row);\n            switch (type) {\n                case 'key':\n                    object = keyOfRow;\n                    break;\n                case 'value':\n                    object = object.get(keyOfRow);\n                    break;\n            }\n        }\n        else {\n            object = object[key];\n        }\n    }\n    return object;\n};\nconst setDeep = (object, path, mapper) => {\n    validatePath(path);\n    if (path.length === 0) {\n        return mapper(object);\n    }\n    let parent = object;\n    for (let i = 0; i < path.length - 1; i++) {\n        const key = path[i];\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(parent)) {\n            const index = +key;\n            parent = parent[index];\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(parent)) {\n            parent = parent[key];\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet)(parent)) {\n            const row = +key;\n            parent = getNthKey(parent, row);\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap)(parent)) {\n            const isEnd = i === path.length - 2;\n            if (isEnd) {\n                break;\n            }\n            const row = +key;\n            const type = +path[++i] === 0 ? 'key' : 'value';\n            const keyOfRow = getNthKey(parent, row);\n            switch (type) {\n                case 'key':\n                    parent = keyOfRow;\n                    break;\n                case 'value':\n                    parent = parent.get(keyOfRow);\n                    break;\n            }\n        }\n    }\n    const lastKey = path[path.length - 1];\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(parent)) {\n        parent[+lastKey] = mapper(parent[+lastKey]);\n    }\n    else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(parent)) {\n        parent[lastKey] = mapper(parent[lastKey]);\n    }\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet)(parent)) {\n        const oldValue = getNthKey(parent, +lastKey);\n        const newValue = mapper(oldValue);\n        if (oldValue !== newValue) {\n            parent.delete(oldValue);\n            parent.add(newValue);\n        }\n    }\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap)(parent)) {\n        const row = +path[path.length - 2];\n        const keyToRow = getNthKey(parent, row);\n        const type = +lastKey === 0 ? 'key' : 'value';\n        switch (type) {\n            case 'key': {\n                const newKey = mapper(keyToRow);\n                parent.set(newKey, parent.get(keyToRow));\n                if (newKey !== keyToRow) {\n                    parent.delete(keyToRow);\n                }\n                break;\n            }\n            case 'value': {\n                parent.set(keyToRow, mapper(parent.get(keyToRow)));\n                break;\n            }\n        }\n    }\n    return object;\n};\n//# sourceMappingURL=accessDeep.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/accessDeep.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/class-registry.js":
/*!*******************************************************!*\
  !*** ./node_modules/superjson/dist/class-registry.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClassRegistry: () => (/* binding */ ClassRegistry)\n/* harmony export */ });\n/* harmony import */ var _registry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./registry.js */ \"(ssr)/./node_modules/superjson/dist/registry.js\");\n\nclass ClassRegistry extends _registry_js__WEBPACK_IMPORTED_MODULE_0__.Registry {\n    constructor() {\n        super(c => c.name);\n        this.classToAllowedProps = new Map();\n    }\n    register(value, options) {\n        if (typeof options === 'object') {\n            if (options.allowProps) {\n                this.classToAllowedProps.set(value, options.allowProps);\n            }\n            super.register(value, options.identifier);\n        }\n        else {\n            super.register(value, options);\n        }\n    }\n    getAllowedProps(value) {\n        return this.classToAllowedProps.get(value);\n    }\n}\n//# sourceMappingURL=class-registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvY2xhc3MtcmVnaXN0cnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7QUFDbEMsNEJBQTRCLGtEQUFRO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxDdXJzb3IgUHJvamVjdFxcQXVnbWVudFxccHJvbXB0LW1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcc3VwZXJqc29uXFxkaXN0XFxjbGFzcy1yZWdpc3RyeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBSZWdpc3RyeSB9IGZyb20gJy4vcmVnaXN0cnkuanMnO1xuZXhwb3J0IGNsYXNzIENsYXNzUmVnaXN0cnkgZXh0ZW5kcyBSZWdpc3RyeSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKGMgPT4gYy5uYW1lKTtcbiAgICAgICAgdGhpcy5jbGFzc1RvQWxsb3dlZFByb3BzID0gbmV3IE1hcCgpO1xuICAgIH1cbiAgICByZWdpc3Rlcih2YWx1ZSwgb3B0aW9ucykge1xuICAgICAgICBpZiAodHlwZW9mIG9wdGlvbnMgPT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgICBpZiAob3B0aW9ucy5hbGxvd1Byb3BzKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5jbGFzc1RvQWxsb3dlZFByb3BzLnNldCh2YWx1ZSwgb3B0aW9ucy5hbGxvd1Byb3BzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHN1cGVyLnJlZ2lzdGVyKHZhbHVlLCBvcHRpb25zLmlkZW50aWZpZXIpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgc3VwZXIucmVnaXN0ZXIodmFsdWUsIG9wdGlvbnMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGdldEFsbG93ZWRQcm9wcyh2YWx1ZSkge1xuICAgICAgICByZXR1cm4gdGhpcy5jbGFzc1RvQWxsb3dlZFByb3BzLmdldCh2YWx1ZSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2xhc3MtcmVnaXN0cnkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/class-registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/custom-transformer-registry.js":
/*!********************************************************************!*\
  !*** ./node_modules/superjson/dist/custom-transformer-registry.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomTransformerRegistry: () => (/* binding */ CustomTransformerRegistry)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/superjson/dist/util.js\");\n\nclass CustomTransformerRegistry {\n    constructor() {\n        this.transfomers = {};\n    }\n    register(transformer) {\n        this.transfomers[transformer.name] = transformer;\n    }\n    findApplicable(v) {\n        return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.find)(this.transfomers, transformer => transformer.isApplicable(v));\n    }\n    findByName(name) {\n        return this.transfomers[name];\n    }\n}\n//# sourceMappingURL=custom-transformer-registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvY3VzdG9tLXRyYW5zZm9ybWVyLXJlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlDO0FBQzFCO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLDhDQUFJO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXEN1cnNvciBQcm9qZWN0XFxBdWdtZW50XFxwcm9tcHQtbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxzdXBlcmpzb25cXGRpc3RcXGN1c3RvbS10cmFuc2Zvcm1lci1yZWdpc3RyeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmaW5kIH0gZnJvbSAnLi91dGlsLmpzJztcbmV4cG9ydCBjbGFzcyBDdXN0b21UcmFuc2Zvcm1lclJlZ2lzdHJ5IHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgdGhpcy50cmFuc2ZvbWVycyA9IHt9O1xuICAgIH1cbiAgICByZWdpc3Rlcih0cmFuc2Zvcm1lcikge1xuICAgICAgICB0aGlzLnRyYW5zZm9tZXJzW3RyYW5zZm9ybWVyLm5hbWVdID0gdHJhbnNmb3JtZXI7XG4gICAgfVxuICAgIGZpbmRBcHBsaWNhYmxlKHYpIHtcbiAgICAgICAgcmV0dXJuIGZpbmQodGhpcy50cmFuc2ZvbWVycywgdHJhbnNmb3JtZXIgPT4gdHJhbnNmb3JtZXIuaXNBcHBsaWNhYmxlKHYpKTtcbiAgICB9XG4gICAgZmluZEJ5TmFtZShuYW1lKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnRyYW5zZm9tZXJzW25hbWVdO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWN1c3RvbS10cmFuc2Zvcm1lci1yZWdpc3RyeS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/custom-transformer-registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/double-indexed-kv.js":
/*!**********************************************************!*\
  !*** ./node_modules/superjson/dist/double-indexed-kv.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DoubleIndexedKV: () => (/* binding */ DoubleIndexedKV)\n/* harmony export */ });\nclass DoubleIndexedKV {\n    constructor() {\n        this.keyToValue = new Map();\n        this.valueToKey = new Map();\n    }\n    set(key, value) {\n        this.keyToValue.set(key, value);\n        this.valueToKey.set(value, key);\n    }\n    getByKey(key) {\n        return this.keyToValue.get(key);\n    }\n    getByValue(value) {\n        return this.valueToKey.get(value);\n    }\n    clear() {\n        this.keyToValue.clear();\n        this.valueToKey.clear();\n    }\n}\n//# sourceMappingURL=double-indexed-kv.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvZG91YmxlLWluZGV4ZWQta3YuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXEN1cnNvciBQcm9qZWN0XFxBdWdtZW50XFxwcm9tcHQtbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxzdXBlcmpzb25cXGRpc3RcXGRvdWJsZS1pbmRleGVkLWt2LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBEb3VibGVJbmRleGVkS1Yge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICB0aGlzLmtleVRvVmFsdWUgPSBuZXcgTWFwKCk7XG4gICAgICAgIHRoaXMudmFsdWVUb0tleSA9IG5ldyBNYXAoKTtcbiAgICB9XG4gICAgc2V0KGtleSwgdmFsdWUpIHtcbiAgICAgICAgdGhpcy5rZXlUb1ZhbHVlLnNldChrZXksIHZhbHVlKTtcbiAgICAgICAgdGhpcy52YWx1ZVRvS2V5LnNldCh2YWx1ZSwga2V5KTtcbiAgICB9XG4gICAgZ2V0QnlLZXkoa2V5KSB7XG4gICAgICAgIHJldHVybiB0aGlzLmtleVRvVmFsdWUuZ2V0KGtleSk7XG4gICAgfVxuICAgIGdldEJ5VmFsdWUodmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudmFsdWVUb0tleS5nZXQodmFsdWUpO1xuICAgIH1cbiAgICBjbGVhcigpIHtcbiAgICAgICAgdGhpcy5rZXlUb1ZhbHVlLmNsZWFyKCk7XG4gICAgICAgIHRoaXMudmFsdWVUb0tleS5jbGVhcigpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRvdWJsZS1pbmRleGVkLWt2LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/double-indexed-kv.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/index.js":
/*!**********************************************!*\
  !*** ./node_modules/superjson/dist/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SuperJSON: () => (/* binding */ SuperJSON),\n/* harmony export */   allowErrorProps: () => (/* binding */ allowErrorProps),\n/* harmony export */   \"default\": () => (/* binding */ SuperJSON),\n/* harmony export */   deserialize: () => (/* binding */ deserialize),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   registerClass: () => (/* binding */ registerClass),\n/* harmony export */   registerCustom: () => (/* binding */ registerCustom),\n/* harmony export */   registerSymbol: () => (/* binding */ registerSymbol),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\n/* harmony import */ var _class_registry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./class-registry.js */ \"(ssr)/./node_modules/superjson/dist/class-registry.js\");\n/* harmony import */ var _registry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./registry.js */ \"(ssr)/./node_modules/superjson/dist/registry.js\");\n/* harmony import */ var _custom_transformer_registry_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./custom-transformer-registry.js */ \"(ssr)/./node_modules/superjson/dist/custom-transformer-registry.js\");\n/* harmony import */ var _plainer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./plainer.js */ \"(ssr)/./node_modules/superjson/dist/plainer.js\");\n/* harmony import */ var copy_anything__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! copy-anything */ \"(ssr)/./node_modules/copy-anything/dist/index.js\");\n\n\n\n\n\nclass SuperJSON {\n    /**\n     * @param dedupeReferentialEqualities  If true, SuperJSON will make sure only one instance of referentially equal objects are serialized and the rest are replaced with `null`.\n     */\n    constructor({ dedupe = false, } = {}) {\n        this.classRegistry = new _class_registry_js__WEBPACK_IMPORTED_MODULE_0__.ClassRegistry();\n        this.symbolRegistry = new _registry_js__WEBPACK_IMPORTED_MODULE_1__.Registry(s => s.description ?? '');\n        this.customTransformerRegistry = new _custom_transformer_registry_js__WEBPACK_IMPORTED_MODULE_2__.CustomTransformerRegistry();\n        this.allowedErrorProps = [];\n        this.dedupe = dedupe;\n    }\n    serialize(object) {\n        const identities = new Map();\n        const output = (0,_plainer_js__WEBPACK_IMPORTED_MODULE_3__.walker)(object, identities, this, this.dedupe);\n        const res = {\n            json: output.transformedValue,\n        };\n        if (output.annotations) {\n            res.meta = {\n                ...res.meta,\n                values: output.annotations,\n            };\n        }\n        const equalityAnnotations = (0,_plainer_js__WEBPACK_IMPORTED_MODULE_3__.generateReferentialEqualityAnnotations)(identities, this.dedupe);\n        if (equalityAnnotations) {\n            res.meta = {\n                ...res.meta,\n                referentialEqualities: equalityAnnotations,\n            };\n        }\n        return res;\n    }\n    deserialize(payload) {\n        const { json, meta } = payload;\n        let result = (0,copy_anything__WEBPACK_IMPORTED_MODULE_4__.copy)(json);\n        if (meta?.values) {\n            result = (0,_plainer_js__WEBPACK_IMPORTED_MODULE_3__.applyValueAnnotations)(result, meta.values, this);\n        }\n        if (meta?.referentialEqualities) {\n            result = (0,_plainer_js__WEBPACK_IMPORTED_MODULE_3__.applyReferentialEqualityAnnotations)(result, meta.referentialEqualities);\n        }\n        return result;\n    }\n    stringify(object) {\n        return JSON.stringify(this.serialize(object));\n    }\n    parse(string) {\n        return this.deserialize(JSON.parse(string));\n    }\n    registerClass(v, options) {\n        this.classRegistry.register(v, options);\n    }\n    registerSymbol(v, identifier) {\n        this.symbolRegistry.register(v, identifier);\n    }\n    registerCustom(transformer, name) {\n        this.customTransformerRegistry.register({\n            name,\n            ...transformer,\n        });\n    }\n    allowErrorProps(...props) {\n        this.allowedErrorProps.push(...props);\n    }\n}\nSuperJSON.defaultInstance = new SuperJSON();\nSuperJSON.serialize = SuperJSON.defaultInstance.serialize.bind(SuperJSON.defaultInstance);\nSuperJSON.deserialize = SuperJSON.defaultInstance.deserialize.bind(SuperJSON.defaultInstance);\nSuperJSON.stringify = SuperJSON.defaultInstance.stringify.bind(SuperJSON.defaultInstance);\nSuperJSON.parse = SuperJSON.defaultInstance.parse.bind(SuperJSON.defaultInstance);\nSuperJSON.registerClass = SuperJSON.defaultInstance.registerClass.bind(SuperJSON.defaultInstance);\nSuperJSON.registerSymbol = SuperJSON.defaultInstance.registerSymbol.bind(SuperJSON.defaultInstance);\nSuperJSON.registerCustom = SuperJSON.defaultInstance.registerCustom.bind(SuperJSON.defaultInstance);\nSuperJSON.allowErrorProps = SuperJSON.defaultInstance.allowErrorProps.bind(SuperJSON.defaultInstance);\n\nconst serialize = SuperJSON.serialize;\nconst deserialize = SuperJSON.deserialize;\nconst stringify = SuperJSON.stringify;\nconst parse = SuperJSON.parse;\nconst registerClass = SuperJSON.registerClass;\nconst registerCustom = SuperJSON.registerCustom;\nconst registerSymbol = SuperJSON.registerSymbol;\nconst allowErrorProps = SuperJSON.allowErrorProps;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/is.js":
/*!*******************************************!*\
  !*** ./node_modules/superjson/dist/is.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isArray: () => (/* binding */ isArray),\n/* harmony export */   isBigint: () => (/* binding */ isBigint),\n/* harmony export */   isBoolean: () => (/* binding */ isBoolean),\n/* harmony export */   isDate: () => (/* binding */ isDate),\n/* harmony export */   isEmptyObject: () => (/* binding */ isEmptyObject),\n/* harmony export */   isError: () => (/* binding */ isError),\n/* harmony export */   isInfinite: () => (/* binding */ isInfinite),\n/* harmony export */   isMap: () => (/* binding */ isMap),\n/* harmony export */   isNaNValue: () => (/* binding */ isNaNValue),\n/* harmony export */   isNull: () => (/* binding */ isNull),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isPrimitive: () => (/* binding */ isPrimitive),\n/* harmony export */   isRegExp: () => (/* binding */ isRegExp),\n/* harmony export */   isSet: () => (/* binding */ isSet),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   isSymbol: () => (/* binding */ isSymbol),\n/* harmony export */   isTypedArray: () => (/* binding */ isTypedArray),\n/* harmony export */   isURL: () => (/* binding */ isURL),\n/* harmony export */   isUndefined: () => (/* binding */ isUndefined)\n/* harmony export */ });\nconst getType = (payload) => Object.prototype.toString.call(payload).slice(8, -1);\nconst isUndefined = (payload) => typeof payload === 'undefined';\nconst isNull = (payload) => payload === null;\nconst isPlainObject = (payload) => {\n    if (typeof payload !== 'object' || payload === null)\n        return false;\n    if (payload === Object.prototype)\n        return false;\n    if (Object.getPrototypeOf(payload) === null)\n        return true;\n    return Object.getPrototypeOf(payload) === Object.prototype;\n};\nconst isEmptyObject = (payload) => isPlainObject(payload) && Object.keys(payload).length === 0;\nconst isArray = (payload) => Array.isArray(payload);\nconst isString = (payload) => typeof payload === 'string';\nconst isNumber = (payload) => typeof payload === 'number' && !isNaN(payload);\nconst isBoolean = (payload) => typeof payload === 'boolean';\nconst isRegExp = (payload) => payload instanceof RegExp;\nconst isMap = (payload) => payload instanceof Map;\nconst isSet = (payload) => payload instanceof Set;\nconst isSymbol = (payload) => getType(payload) === 'Symbol';\nconst isDate = (payload) => payload instanceof Date && !isNaN(payload.valueOf());\nconst isError = (payload) => payload instanceof Error;\nconst isNaNValue = (payload) => typeof payload === 'number' && isNaN(payload);\nconst isPrimitive = (payload) => isBoolean(payload) ||\n    isNull(payload) ||\n    isUndefined(payload) ||\n    isNumber(payload) ||\n    isString(payload) ||\n    isSymbol(payload);\nconst isBigint = (payload) => typeof payload === 'bigint';\nconst isInfinite = (payload) => payload === Infinity || payload === -Infinity;\nconst isTypedArray = (payload) => ArrayBuffer.isView(payload) && !(payload instanceof DataView);\nconst isURL = (payload) => payload instanceof URL;\n//# sourceMappingURL=is.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/is.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/pathstringifier.js":
/*!********************************************************!*\
  !*** ./node_modules/superjson/dist/pathstringifier.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   escapeKey: () => (/* binding */ escapeKey),\n/* harmony export */   parsePath: () => (/* binding */ parsePath),\n/* harmony export */   stringifyPath: () => (/* binding */ stringifyPath)\n/* harmony export */ });\nconst escapeKey = (key) => key.replace(/\\./g, '\\\\.');\nconst stringifyPath = (path) => path\n    .map(String)\n    .map(escapeKey)\n    .join('.');\nconst parsePath = (string) => {\n    const result = [];\n    let segment = '';\n    for (let i = 0; i < string.length; i++) {\n        let char = string.charAt(i);\n        const isEscapedDot = char === '\\\\' && string.charAt(i + 1) === '.';\n        if (isEscapedDot) {\n            segment += '.';\n            i++;\n            continue;\n        }\n        const isEndOfSegment = char === '.';\n        if (isEndOfSegment) {\n            result.push(segment);\n            segment = '';\n            continue;\n        }\n        segment += char;\n    }\n    const lastSegment = segment;\n    result.push(lastSegment);\n    return result;\n};\n//# sourceMappingURL=pathstringifier.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvcGF0aHN0cmluZ2lmaWVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxvQkFBb0IsbUJBQW1CO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxDdXJzb3IgUHJvamVjdFxcQXVnbWVudFxccHJvbXB0LW1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcc3VwZXJqc29uXFxkaXN0XFxwYXRoc3RyaW5naWZpZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGVzY2FwZUtleSA9IChrZXkpID0+IGtleS5yZXBsYWNlKC9cXC4vZywgJ1xcXFwuJyk7XG5leHBvcnQgY29uc3Qgc3RyaW5naWZ5UGF0aCA9IChwYXRoKSA9PiBwYXRoXG4gICAgLm1hcChTdHJpbmcpXG4gICAgLm1hcChlc2NhcGVLZXkpXG4gICAgLmpvaW4oJy4nKTtcbmV4cG9ydCBjb25zdCBwYXJzZVBhdGggPSAoc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgcmVzdWx0ID0gW107XG4gICAgbGV0IHNlZ21lbnQgPSAnJztcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHN0cmluZy5sZW5ndGg7IGkrKykge1xuICAgICAgICBsZXQgY2hhciA9IHN0cmluZy5jaGFyQXQoaSk7XG4gICAgICAgIGNvbnN0IGlzRXNjYXBlZERvdCA9IGNoYXIgPT09ICdcXFxcJyAmJiBzdHJpbmcuY2hhckF0KGkgKyAxKSA9PT0gJy4nO1xuICAgICAgICBpZiAoaXNFc2NhcGVkRG90KSB7XG4gICAgICAgICAgICBzZWdtZW50ICs9ICcuJztcbiAgICAgICAgICAgIGkrKztcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGlzRW5kT2ZTZWdtZW50ID0gY2hhciA9PT0gJy4nO1xuICAgICAgICBpZiAoaXNFbmRPZlNlZ21lbnQpIHtcbiAgICAgICAgICAgIHJlc3VsdC5wdXNoKHNlZ21lbnQpO1xuICAgICAgICAgICAgc2VnbWVudCA9ICcnO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgc2VnbWVudCArPSBjaGFyO1xuICAgIH1cbiAgICBjb25zdCBsYXN0U2VnbWVudCA9IHNlZ21lbnQ7XG4gICAgcmVzdWx0LnB1c2gobGFzdFNlZ21lbnQpO1xuICAgIHJldHVybiByZXN1bHQ7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGF0aHN0cmluZ2lmaWVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/pathstringifier.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/plainer.js":
/*!************************************************!*\
  !*** ./node_modules/superjson/dist/plainer.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyReferentialEqualityAnnotations: () => (/* binding */ applyReferentialEqualityAnnotations),\n/* harmony export */   applyValueAnnotations: () => (/* binding */ applyValueAnnotations),\n/* harmony export */   generateReferentialEqualityAnnotations: () => (/* binding */ generateReferentialEqualityAnnotations),\n/* harmony export */   walker: () => (/* binding */ walker)\n/* harmony export */ });\n/* harmony import */ var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is.js */ \"(ssr)/./node_modules/superjson/dist/is.js\");\n/* harmony import */ var _pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pathstringifier.js */ \"(ssr)/./node_modules/superjson/dist/pathstringifier.js\");\n/* harmony import */ var _transformer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./transformer.js */ \"(ssr)/./node_modules/superjson/dist/transformer.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/superjson/dist/util.js\");\n/* harmony import */ var _accessDeep_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./accessDeep.js */ \"(ssr)/./node_modules/superjson/dist/accessDeep.js\");\n\n\n\n\n\n\nfunction traverse(tree, walker, origin = []) {\n    if (!tree) {\n        return;\n    }\n    if (!(0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(tree)) {\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(tree, (subtree, key) => traverse(subtree, walker, [...origin, ...(0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath)(key)]));\n        return;\n    }\n    const [nodeValue, children] = tree;\n    if (children) {\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(children, (child, key) => {\n            traverse(child, walker, [...origin, ...(0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath)(key)]);\n        });\n    }\n    walker(nodeValue, origin);\n}\nfunction applyValueAnnotations(plain, annotations, superJson) {\n    traverse(annotations, (type, path) => {\n        plain = (0,_accessDeep_js__WEBPACK_IMPORTED_MODULE_4__.setDeep)(plain, path, v => (0,_transformer_js__WEBPACK_IMPORTED_MODULE_2__.untransformValue)(v, type, superJson));\n    });\n    return plain;\n}\nfunction applyReferentialEqualityAnnotations(plain, annotations) {\n    function apply(identicalPaths, path) {\n        const object = (0,_accessDeep_js__WEBPACK_IMPORTED_MODULE_4__.getDeep)(plain, (0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath)(path));\n        identicalPaths.map(_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath).forEach(identicalObjectPath => {\n            plain = (0,_accessDeep_js__WEBPACK_IMPORTED_MODULE_4__.setDeep)(plain, identicalObjectPath, () => object);\n        });\n    }\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(annotations)) {\n        const [root, other] = annotations;\n        root.forEach(identicalPath => {\n            plain = (0,_accessDeep_js__WEBPACK_IMPORTED_MODULE_4__.setDeep)(plain, (0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath)(identicalPath), () => plain);\n        });\n        if (other) {\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(other, apply);\n        }\n    }\n    else {\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(annotations, apply);\n    }\n    return plain;\n}\nconst isDeep = (object, superJson) => (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(object) ||\n    (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(object) ||\n    (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap)(object) ||\n    (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet)(object) ||\n    (0,_transformer_js__WEBPACK_IMPORTED_MODULE_2__.isInstanceOfRegisteredClass)(object, superJson);\nfunction addIdentity(object, path, identities) {\n    const existingSet = identities.get(object);\n    if (existingSet) {\n        existingSet.push(path);\n    }\n    else {\n        identities.set(object, [path]);\n    }\n}\nfunction generateReferentialEqualityAnnotations(identitites, dedupe) {\n    const result = {};\n    let rootEqualityPaths = undefined;\n    identitites.forEach(paths => {\n        if (paths.length <= 1) {\n            return;\n        }\n        // if we're not deduping, all of these objects continue existing.\n        // putting the shortest path first makes it easier to parse for humans\n        // if we're deduping though, only the first entry will still exist, so we can't do this optimisation.\n        if (!dedupe) {\n            paths = paths\n                .map(path => path.map(String))\n                .sort((a, b) => a.length - b.length);\n        }\n        const [representativePath, ...identicalPaths] = paths;\n        if (representativePath.length === 0) {\n            rootEqualityPaths = identicalPaths.map(_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.stringifyPath);\n        }\n        else {\n            result[(0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.stringifyPath)(representativePath)] = identicalPaths.map(_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.stringifyPath);\n        }\n    });\n    if (rootEqualityPaths) {\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isEmptyObject)(result)) {\n            return [rootEqualityPaths];\n        }\n        else {\n            return [rootEqualityPaths, result];\n        }\n    }\n    else {\n        return (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isEmptyObject)(result) ? undefined : result;\n    }\n}\nconst walker = (object, identities, superJson, dedupe, path = [], objectsInThisPath = [], seenObjects = new Map()) => {\n    const primitive = (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPrimitive)(object);\n    if (!primitive) {\n        addIdentity(object, path, identities);\n        const seen = seenObjects.get(object);\n        if (seen) {\n            // short-circuit result if we've seen this object before\n            return dedupe\n                ? {\n                    transformedValue: null,\n                }\n                : seen;\n        }\n    }\n    if (!isDeep(object, superJson)) {\n        const transformed = (0,_transformer_js__WEBPACK_IMPORTED_MODULE_2__.transformValue)(object, superJson);\n        const result = transformed\n            ? {\n                transformedValue: transformed.value,\n                annotations: [transformed.type],\n            }\n            : {\n                transformedValue: object,\n            };\n        if (!primitive) {\n            seenObjects.set(object, result);\n        }\n        return result;\n    }\n    if ((0,_util_js__WEBPACK_IMPORTED_MODULE_3__.includes)(objectsInThisPath, object)) {\n        // prevent circular references\n        return {\n            transformedValue: null,\n        };\n    }\n    const transformationResult = (0,_transformer_js__WEBPACK_IMPORTED_MODULE_2__.transformValue)(object, superJson);\n    const transformed = transformationResult?.value ?? object;\n    const transformedValue = (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(transformed) ? [] : {};\n    const innerAnnotations = {};\n    (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(transformed, (value, index) => {\n        if (index === '__proto__' ||\n            index === 'constructor' ||\n            index === 'prototype') {\n            throw new Error(`Detected property ${index}. This is a prototype pollution risk, please remove it from your object.`);\n        }\n        const recursiveResult = walker(value, identities, superJson, dedupe, [...path, index], [...objectsInThisPath, object], seenObjects);\n        transformedValue[index] = recursiveResult.transformedValue;\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(recursiveResult.annotations)) {\n            innerAnnotations[index] = recursiveResult.annotations;\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(recursiveResult.annotations)) {\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(recursiveResult.annotations, (tree, key) => {\n                innerAnnotations[(0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.escapeKey)(index) + '.' + key] = tree;\n            });\n        }\n    });\n    const result = (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isEmptyObject)(innerAnnotations)\n        ? {\n            transformedValue,\n            annotations: !!transformationResult\n                ? [transformationResult.type]\n                : undefined,\n        }\n        : {\n            transformedValue,\n            annotations: !!transformationResult\n                ? [transformationResult.type, innerAnnotations]\n                : innerAnnotations,\n        };\n    if (!primitive) {\n        seenObjects.set(object, result);\n    }\n    return result;\n};\n//# sourceMappingURL=plainer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/plainer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/registry.js":
/*!*************************************************!*\
  !*** ./node_modules/superjson/dist/registry.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Registry: () => (/* binding */ Registry)\n/* harmony export */ });\n/* harmony import */ var _double_indexed_kv_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./double-indexed-kv.js */ \"(ssr)/./node_modules/superjson/dist/double-indexed-kv.js\");\n\nclass Registry {\n    constructor(generateIdentifier) {\n        this.generateIdentifier = generateIdentifier;\n        this.kv = new _double_indexed_kv_js__WEBPACK_IMPORTED_MODULE_0__.DoubleIndexedKV();\n    }\n    register(value, identifier) {\n        if (this.kv.getByValue(value)) {\n            return;\n        }\n        if (!identifier) {\n            identifier = this.generateIdentifier(value);\n        }\n        this.kv.set(identifier, value);\n    }\n    clear() {\n        this.kv.clear();\n    }\n    getIdentifier(value) {\n        return this.kv.getByValue(value);\n    }\n    getValue(identifier) {\n        return this.kv.getByKey(identifier);\n    }\n}\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvcmVnaXN0cnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUQ7QUFDbEQ7QUFDUDtBQUNBO0FBQ0Esc0JBQXNCLGtFQUFlO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXEN1cnNvciBQcm9qZWN0XFxBdWdtZW50XFxwcm9tcHQtbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxzdXBlcmpzb25cXGRpc3RcXHJlZ2lzdHJ5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IERvdWJsZUluZGV4ZWRLViB9IGZyb20gJy4vZG91YmxlLWluZGV4ZWQta3YuanMnO1xuZXhwb3J0IGNsYXNzIFJlZ2lzdHJ5IHtcbiAgICBjb25zdHJ1Y3RvcihnZW5lcmF0ZUlkZW50aWZpZXIpIHtcbiAgICAgICAgdGhpcy5nZW5lcmF0ZUlkZW50aWZpZXIgPSBnZW5lcmF0ZUlkZW50aWZpZXI7XG4gICAgICAgIHRoaXMua3YgPSBuZXcgRG91YmxlSW5kZXhlZEtWKCk7XG4gICAgfVxuICAgIHJlZ2lzdGVyKHZhbHVlLCBpZGVudGlmaWVyKSB7XG4gICAgICAgIGlmICh0aGlzLmt2LmdldEJ5VmFsdWUodmFsdWUpKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFpZGVudGlmaWVyKSB7XG4gICAgICAgICAgICBpZGVudGlmaWVyID0gdGhpcy5nZW5lcmF0ZUlkZW50aWZpZXIodmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMua3Yuc2V0KGlkZW50aWZpZXIsIHZhbHVlKTtcbiAgICB9XG4gICAgY2xlYXIoKSB7XG4gICAgICAgIHRoaXMua3YuY2xlYXIoKTtcbiAgICB9XG4gICAgZ2V0SWRlbnRpZmllcih2YWx1ZSkge1xuICAgICAgICByZXR1cm4gdGhpcy5rdi5nZXRCeVZhbHVlKHZhbHVlKTtcbiAgICB9XG4gICAgZ2V0VmFsdWUoaWRlbnRpZmllcikge1xuICAgICAgICByZXR1cm4gdGhpcy5rdi5nZXRCeUtleShpZGVudGlmaWVyKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWdpc3RyeS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/transformer.js":
/*!****************************************************!*\
  !*** ./node_modules/superjson/dist/transformer.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isInstanceOfRegisteredClass: () => (/* binding */ isInstanceOfRegisteredClass),\n/* harmony export */   transformValue: () => (/* binding */ transformValue),\n/* harmony export */   untransformValue: () => (/* binding */ untransformValue)\n/* harmony export */ });\n/* harmony import */ var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is.js */ \"(ssr)/./node_modules/superjson/dist/is.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/superjson/dist/util.js\");\n\n\nfunction simpleTransformation(isApplicable, annotation, transform, untransform) {\n    return {\n        isApplicable,\n        annotation,\n        transform,\n        untransform,\n    };\n}\nconst simpleRules = [\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isUndefined, 'undefined', () => null, () => undefined),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isBigint, 'bigint', v => v.toString(), v => {\n        if (typeof BigInt !== 'undefined') {\n            return BigInt(v);\n        }\n        console.error('Please add a BigInt polyfill.');\n        return v;\n    }),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isDate, 'Date', v => v.toISOString(), v => new Date(v)),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isError, 'Error', (v, superJson) => {\n        const baseError = {\n            name: v.name,\n            message: v.message,\n        };\n        superJson.allowedErrorProps.forEach(prop => {\n            baseError[prop] = v[prop];\n        });\n        return baseError;\n    }, (v, superJson) => {\n        const e = new Error(v.message);\n        e.name = v.name;\n        e.stack = v.stack;\n        superJson.allowedErrorProps.forEach(prop => {\n            e[prop] = v[prop];\n        });\n        return e;\n    }),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isRegExp, 'regexp', v => '' + v, regex => {\n        const body = regex.slice(1, regex.lastIndexOf('/'));\n        const flags = regex.slice(regex.lastIndexOf('/') + 1);\n        return new RegExp(body, flags);\n    }),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet, 'set', \n    // (sets only exist in es6+)\n    // eslint-disable-next-line es5/no-es6-methods\n    v => [...v.values()], v => new Set(v)),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap, 'map', v => [...v.entries()], v => new Map(v)),\n    simpleTransformation((v) => (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isNaNValue)(v) || (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isInfinite)(v), 'number', v => {\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isNaNValue)(v)) {\n            return 'NaN';\n        }\n        if (v > 0) {\n            return 'Infinity';\n        }\n        else {\n            return '-Infinity';\n        }\n    }, Number),\n    simpleTransformation((v) => v === 0 && 1 / v === -Infinity, 'number', () => {\n        return '-0';\n    }, Number),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isURL, 'URL', v => v.toString(), v => new URL(v)),\n];\nfunction compositeTransformation(isApplicable, annotation, transform, untransform) {\n    return {\n        isApplicable,\n        annotation,\n        transform,\n        untransform,\n    };\n}\nconst symbolRule = compositeTransformation((s, superJson) => {\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSymbol)(s)) {\n        const isRegistered = !!superJson.symbolRegistry.getIdentifier(s);\n        return isRegistered;\n    }\n    return false;\n}, (s, superJson) => {\n    const identifier = superJson.symbolRegistry.getIdentifier(s);\n    return ['symbol', identifier];\n}, v => v.description, (_, a, superJson) => {\n    const value = superJson.symbolRegistry.getValue(a[1]);\n    if (!value) {\n        throw new Error('Trying to deserialize unknown symbol');\n    }\n    return value;\n});\nconst constructorToName = [\n    Int8Array,\n    Uint8Array,\n    Int16Array,\n    Uint16Array,\n    Int32Array,\n    Uint32Array,\n    Float32Array,\n    Float64Array,\n    Uint8ClampedArray,\n].reduce((obj, ctor) => {\n    obj[ctor.name] = ctor;\n    return obj;\n}, {});\nconst typedArrayRule = compositeTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isTypedArray, v => ['typed-array', v.constructor.name], v => [...v], (v, a) => {\n    const ctor = constructorToName[a[1]];\n    if (!ctor) {\n        throw new Error('Trying to deserialize unknown typed array');\n    }\n    return new ctor(v);\n});\nfunction isInstanceOfRegisteredClass(potentialClass, superJson) {\n    if (potentialClass?.constructor) {\n        const isRegistered = !!superJson.classRegistry.getIdentifier(potentialClass.constructor);\n        return isRegistered;\n    }\n    return false;\n}\nconst classRule = compositeTransformation(isInstanceOfRegisteredClass, (clazz, superJson) => {\n    const identifier = superJson.classRegistry.getIdentifier(clazz.constructor);\n    return ['class', identifier];\n}, (clazz, superJson) => {\n    const allowedProps = superJson.classRegistry.getAllowedProps(clazz.constructor);\n    if (!allowedProps) {\n        return { ...clazz };\n    }\n    const result = {};\n    allowedProps.forEach(prop => {\n        result[prop] = clazz[prop];\n    });\n    return result;\n}, (v, a, superJson) => {\n    const clazz = superJson.classRegistry.getValue(a[1]);\n    if (!clazz) {\n        throw new Error(`Trying to deserialize unknown class '${a[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);\n    }\n    return Object.assign(Object.create(clazz.prototype), v);\n});\nconst customRule = compositeTransformation((value, superJson) => {\n    return !!superJson.customTransformerRegistry.findApplicable(value);\n}, (value, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findApplicable(value);\n    return ['custom', transformer.name];\n}, (value, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findApplicable(value);\n    return transformer.serialize(value);\n}, (v, a, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findByName(a[1]);\n    if (!transformer) {\n        throw new Error('Trying to deserialize unknown custom value');\n    }\n    return transformer.deserialize(v);\n});\nconst compositeRules = [classRule, symbolRule, customRule, typedArrayRule];\nconst transformValue = (value, superJson) => {\n    const applicableCompositeRule = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.findArr)(compositeRules, rule => rule.isApplicable(value, superJson));\n    if (applicableCompositeRule) {\n        return {\n            value: applicableCompositeRule.transform(value, superJson),\n            type: applicableCompositeRule.annotation(value, superJson),\n        };\n    }\n    const applicableSimpleRule = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.findArr)(simpleRules, rule => rule.isApplicable(value, superJson));\n    if (applicableSimpleRule) {\n        return {\n            value: applicableSimpleRule.transform(value, superJson),\n            type: applicableSimpleRule.annotation,\n        };\n    }\n    return undefined;\n};\nconst simpleRulesByAnnotation = {};\nsimpleRules.forEach(rule => {\n    simpleRulesByAnnotation[rule.annotation] = rule;\n});\nconst untransformValue = (json, type, superJson) => {\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(type)) {\n        switch (type[0]) {\n            case 'symbol':\n                return symbolRule.untransform(json, type, superJson);\n            case 'class':\n                return classRule.untransform(json, type, superJson);\n            case 'custom':\n                return customRule.untransform(json, type, superJson);\n            case 'typed-array':\n                return typedArrayRule.untransform(json, type, superJson);\n            default:\n                throw new Error('Unknown transformation: ' + type);\n        }\n    }\n    else {\n        const transformation = simpleRulesByAnnotation[type];\n        if (!transformation) {\n            throw new Error('Unknown transformation: ' + type);\n        }\n        return transformation.untransform(json, superJson);\n    }\n};\n//# sourceMappingURL=transformer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvdHJhbnNmb3JtZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEo7QUFDdEg7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLCtDQUFXO0FBQ3BDLHlCQUF5Qiw0Q0FBUTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLHlCQUF5QiwwQ0FBTTtBQUMvQix5QkFBeUIsMkNBQU87QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0wseUJBQXlCLDRDQUFRO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCx5QkFBeUIseUNBQUs7QUFDOUI7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHlDQUFLO0FBQzlCLGdDQUFnQyxrREFBVSxPQUFPLGtEQUFVO0FBQzNELFlBQVksa0RBQVU7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMLHlCQUF5Qix5Q0FBSztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsZ0RBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxJQUFJO0FBQ0wsK0NBQStDLGdEQUFZO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ007QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsZ0VBQWdFLEtBQUs7QUFDckU7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ087QUFDUCxvQ0FBb0MsaURBQU87QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLGlEQUFPO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ007QUFDUCxRQUFRLCtDQUFPO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxDdXJzb3IgUHJvamVjdFxcQXVnbWVudFxccHJvbXB0LW1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcc3VwZXJqc29uXFxkaXN0XFx0cmFuc2Zvcm1lci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0JpZ2ludCwgaXNEYXRlLCBpc0luZmluaXRlLCBpc01hcCwgaXNOYU5WYWx1ZSwgaXNSZWdFeHAsIGlzU2V0LCBpc1VuZGVmaW5lZCwgaXNTeW1ib2wsIGlzQXJyYXksIGlzRXJyb3IsIGlzVHlwZWRBcnJheSwgaXNVUkwsIH0gZnJvbSAnLi9pcy5qcyc7XG5pbXBvcnQgeyBmaW5kQXJyIH0gZnJvbSAnLi91dGlsLmpzJztcbmZ1bmN0aW9uIHNpbXBsZVRyYW5zZm9ybWF0aW9uKGlzQXBwbGljYWJsZSwgYW5ub3RhdGlvbiwgdHJhbnNmb3JtLCB1bnRyYW5zZm9ybSkge1xuICAgIHJldHVybiB7XG4gICAgICAgIGlzQXBwbGljYWJsZSxcbiAgICAgICAgYW5ub3RhdGlvbixcbiAgICAgICAgdHJhbnNmb3JtLFxuICAgICAgICB1bnRyYW5zZm9ybSxcbiAgICB9O1xufVxuY29uc3Qgc2ltcGxlUnVsZXMgPSBbXG4gICAgc2ltcGxlVHJhbnNmb3JtYXRpb24oaXNVbmRlZmluZWQsICd1bmRlZmluZWQnLCAoKSA9PiBudWxsLCAoKSA9PiB1bmRlZmluZWQpLFxuICAgIHNpbXBsZVRyYW5zZm9ybWF0aW9uKGlzQmlnaW50LCAnYmlnaW50JywgdiA9PiB2LnRvU3RyaW5nKCksIHYgPT4ge1xuICAgICAgICBpZiAodHlwZW9mIEJpZ0ludCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHJldHVybiBCaWdJbnQodik7XG4gICAgICAgIH1cbiAgICAgICAgY29uc29sZS5lcnJvcignUGxlYXNlIGFkZCBhIEJpZ0ludCBwb2x5ZmlsbC4nKTtcbiAgICAgICAgcmV0dXJuIHY7XG4gICAgfSksXG4gICAgc2ltcGxlVHJhbnNmb3JtYXRpb24oaXNEYXRlLCAnRGF0ZScsIHYgPT4gdi50b0lTT1N0cmluZygpLCB2ID0+IG5ldyBEYXRlKHYpKSxcbiAgICBzaW1wbGVUcmFuc2Zvcm1hdGlvbihpc0Vycm9yLCAnRXJyb3InLCAodiwgc3VwZXJKc29uKSA9PiB7XG4gICAgICAgIGNvbnN0IGJhc2VFcnJvciA9IHtcbiAgICAgICAgICAgIG5hbWU6IHYubmFtZSxcbiAgICAgICAgICAgIG1lc3NhZ2U6IHYubWVzc2FnZSxcbiAgICAgICAgfTtcbiAgICAgICAgc3VwZXJKc29uLmFsbG93ZWRFcnJvclByb3BzLmZvckVhY2gocHJvcCA9PiB7XG4gICAgICAgICAgICBiYXNlRXJyb3JbcHJvcF0gPSB2W3Byb3BdO1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIGJhc2VFcnJvcjtcbiAgICB9LCAodiwgc3VwZXJKc29uKSA9PiB7XG4gICAgICAgIGNvbnN0IGUgPSBuZXcgRXJyb3Iodi5tZXNzYWdlKTtcbiAgICAgICAgZS5uYW1lID0gdi5uYW1lO1xuICAgICAgICBlLnN0YWNrID0gdi5zdGFjaztcbiAgICAgICAgc3VwZXJKc29uLmFsbG93ZWRFcnJvclByb3BzLmZvckVhY2gocHJvcCA9PiB7XG4gICAgICAgICAgICBlW3Byb3BdID0gdltwcm9wXTtcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBlO1xuICAgIH0pLFxuICAgIHNpbXBsZVRyYW5zZm9ybWF0aW9uKGlzUmVnRXhwLCAncmVnZXhwJywgdiA9PiAnJyArIHYsIHJlZ2V4ID0+IHtcbiAgICAgICAgY29uc3QgYm9keSA9IHJlZ2V4LnNsaWNlKDEsIHJlZ2V4Lmxhc3RJbmRleE9mKCcvJykpO1xuICAgICAgICBjb25zdCBmbGFncyA9IHJlZ2V4LnNsaWNlKHJlZ2V4Lmxhc3RJbmRleE9mKCcvJykgKyAxKTtcbiAgICAgICAgcmV0dXJuIG5ldyBSZWdFeHAoYm9keSwgZmxhZ3MpO1xuICAgIH0pLFxuICAgIHNpbXBsZVRyYW5zZm9ybWF0aW9uKGlzU2V0LCAnc2V0JywgXG4gICAgLy8gKHNldHMgb25seSBleGlzdCBpbiBlczYrKVxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBlczUvbm8tZXM2LW1ldGhvZHNcbiAgICB2ID0+IFsuLi52LnZhbHVlcygpXSwgdiA9PiBuZXcgU2V0KHYpKSxcbiAgICBzaW1wbGVUcmFuc2Zvcm1hdGlvbihpc01hcCwgJ21hcCcsIHYgPT4gWy4uLnYuZW50cmllcygpXSwgdiA9PiBuZXcgTWFwKHYpKSxcbiAgICBzaW1wbGVUcmFuc2Zvcm1hdGlvbigodikgPT4gaXNOYU5WYWx1ZSh2KSB8fCBpc0luZmluaXRlKHYpLCAnbnVtYmVyJywgdiA9PiB7XG4gICAgICAgIGlmIChpc05hTlZhbHVlKHYpKSB7XG4gICAgICAgICAgICByZXR1cm4gJ05hTic7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHYgPiAwKSB7XG4gICAgICAgICAgICByZXR1cm4gJ0luZmluaXR5JztcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiAnLUluZmluaXR5JztcbiAgICAgICAgfVxuICAgIH0sIE51bWJlciksXG4gICAgc2ltcGxlVHJhbnNmb3JtYXRpb24oKHYpID0+IHYgPT09IDAgJiYgMSAvIHYgPT09IC1JbmZpbml0eSwgJ251bWJlcicsICgpID0+IHtcbiAgICAgICAgcmV0dXJuICctMCc7XG4gICAgfSwgTnVtYmVyKSxcbiAgICBzaW1wbGVUcmFuc2Zvcm1hdGlvbihpc1VSTCwgJ1VSTCcsIHYgPT4gdi50b1N0cmluZygpLCB2ID0+IG5ldyBVUkwodikpLFxuXTtcbmZ1bmN0aW9uIGNvbXBvc2l0ZVRyYW5zZm9ybWF0aW9uKGlzQXBwbGljYWJsZSwgYW5ub3RhdGlvbiwgdHJhbnNmb3JtLCB1bnRyYW5zZm9ybSkge1xuICAgIHJldHVybiB7XG4gICAgICAgIGlzQXBwbGljYWJsZSxcbiAgICAgICAgYW5ub3RhdGlvbixcbiAgICAgICAgdHJhbnNmb3JtLFxuICAgICAgICB1bnRyYW5zZm9ybSxcbiAgICB9O1xufVxuY29uc3Qgc3ltYm9sUnVsZSA9IGNvbXBvc2l0ZVRyYW5zZm9ybWF0aW9uKChzLCBzdXBlckpzb24pID0+IHtcbiAgICBpZiAoaXNTeW1ib2wocykpIHtcbiAgICAgICAgY29uc3QgaXNSZWdpc3RlcmVkID0gISFzdXBlckpzb24uc3ltYm9sUmVnaXN0cnkuZ2V0SWRlbnRpZmllcihzKTtcbiAgICAgICAgcmV0dXJuIGlzUmVnaXN0ZXJlZDtcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xufSwgKHMsIHN1cGVySnNvbikgPT4ge1xuICAgIGNvbnN0IGlkZW50aWZpZXIgPSBzdXBlckpzb24uc3ltYm9sUmVnaXN0cnkuZ2V0SWRlbnRpZmllcihzKTtcbiAgICByZXR1cm4gWydzeW1ib2wnLCBpZGVudGlmaWVyXTtcbn0sIHYgPT4gdi5kZXNjcmlwdGlvbiwgKF8sIGEsIHN1cGVySnNvbikgPT4ge1xuICAgIGNvbnN0IHZhbHVlID0gc3VwZXJKc29uLnN5bWJvbFJlZ2lzdHJ5LmdldFZhbHVlKGFbMV0pO1xuICAgIGlmICghdmFsdWUpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdUcnlpbmcgdG8gZGVzZXJpYWxpemUgdW5rbm93biBzeW1ib2wnKTtcbiAgICB9XG4gICAgcmV0dXJuIHZhbHVlO1xufSk7XG5jb25zdCBjb25zdHJ1Y3RvclRvTmFtZSA9IFtcbiAgICBJbnQ4QXJyYXksXG4gICAgVWludDhBcnJheSxcbiAgICBJbnQxNkFycmF5LFxuICAgIFVpbnQxNkFycmF5LFxuICAgIEludDMyQXJyYXksXG4gICAgVWludDMyQXJyYXksXG4gICAgRmxvYXQzMkFycmF5LFxuICAgIEZsb2F0NjRBcnJheSxcbiAgICBVaW50OENsYW1wZWRBcnJheSxcbl0ucmVkdWNlKChvYmosIGN0b3IpID0+IHtcbiAgICBvYmpbY3Rvci5uYW1lXSA9IGN0b3I7XG4gICAgcmV0dXJuIG9iajtcbn0sIHt9KTtcbmNvbnN0IHR5cGVkQXJyYXlSdWxlID0gY29tcG9zaXRlVHJhbnNmb3JtYXRpb24oaXNUeXBlZEFycmF5LCB2ID0+IFsndHlwZWQtYXJyYXknLCB2LmNvbnN0cnVjdG9yLm5hbWVdLCB2ID0+IFsuLi52XSwgKHYsIGEpID0+IHtcbiAgICBjb25zdCBjdG9yID0gY29uc3RydWN0b3JUb05hbWVbYVsxXV07XG4gICAgaWYgKCFjdG9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignVHJ5aW5nIHRvIGRlc2VyaWFsaXplIHVua25vd24gdHlwZWQgYXJyYXknKTtcbiAgICB9XG4gICAgcmV0dXJuIG5ldyBjdG9yKHYpO1xufSk7XG5leHBvcnQgZnVuY3Rpb24gaXNJbnN0YW5jZU9mUmVnaXN0ZXJlZENsYXNzKHBvdGVudGlhbENsYXNzLCBzdXBlckpzb24pIHtcbiAgICBpZiAocG90ZW50aWFsQ2xhc3M/LmNvbnN0cnVjdG9yKSB7XG4gICAgICAgIGNvbnN0IGlzUmVnaXN0ZXJlZCA9ICEhc3VwZXJKc29uLmNsYXNzUmVnaXN0cnkuZ2V0SWRlbnRpZmllcihwb3RlbnRpYWxDbGFzcy5jb25zdHJ1Y3Rvcik7XG4gICAgICAgIHJldHVybiBpc1JlZ2lzdGVyZWQ7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbn1cbmNvbnN0IGNsYXNzUnVsZSA9IGNvbXBvc2l0ZVRyYW5zZm9ybWF0aW9uKGlzSW5zdGFuY2VPZlJlZ2lzdGVyZWRDbGFzcywgKGNsYXp6LCBzdXBlckpzb24pID0+IHtcbiAgICBjb25zdCBpZGVudGlmaWVyID0gc3VwZXJKc29uLmNsYXNzUmVnaXN0cnkuZ2V0SWRlbnRpZmllcihjbGF6ei5jb25zdHJ1Y3Rvcik7XG4gICAgcmV0dXJuIFsnY2xhc3MnLCBpZGVudGlmaWVyXTtcbn0sIChjbGF6eiwgc3VwZXJKc29uKSA9PiB7XG4gICAgY29uc3QgYWxsb3dlZFByb3BzID0gc3VwZXJKc29uLmNsYXNzUmVnaXN0cnkuZ2V0QWxsb3dlZFByb3BzKGNsYXp6LmNvbnN0cnVjdG9yKTtcbiAgICBpZiAoIWFsbG93ZWRQcm9wcykge1xuICAgICAgICByZXR1cm4geyAuLi5jbGF6eiB9O1xuICAgIH1cbiAgICBjb25zdCByZXN1bHQgPSB7fTtcbiAgICBhbGxvd2VkUHJvcHMuZm9yRWFjaChwcm9wID0+IHtcbiAgICAgICAgcmVzdWx0W3Byb3BdID0gY2xhenpbcHJvcF07XG4gICAgfSk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbn0sICh2LCBhLCBzdXBlckpzb24pID0+IHtcbiAgICBjb25zdCBjbGF6eiA9IHN1cGVySnNvbi5jbGFzc1JlZ2lzdHJ5LmdldFZhbHVlKGFbMV0pO1xuICAgIGlmICghY2xhenopIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBUcnlpbmcgdG8gZGVzZXJpYWxpemUgdW5rbm93biBjbGFzcyAnJHthWzFdfScgLSBjaGVjayBodHRwczovL2dpdGh1Yi5jb20vYmxpdHotanMvc3VwZXJqc29uL2lzc3Vlcy8xMTYjaXNzdWVjb21tZW50LTc3Mzk5NjU2NGApO1xuICAgIH1cbiAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihPYmplY3QuY3JlYXRlKGNsYXp6LnByb3RvdHlwZSksIHYpO1xufSk7XG5jb25zdCBjdXN0b21SdWxlID0gY29tcG9zaXRlVHJhbnNmb3JtYXRpb24oKHZhbHVlLCBzdXBlckpzb24pID0+IHtcbiAgICByZXR1cm4gISFzdXBlckpzb24uY3VzdG9tVHJhbnNmb3JtZXJSZWdpc3RyeS5maW5kQXBwbGljYWJsZSh2YWx1ZSk7XG59LCAodmFsdWUsIHN1cGVySnNvbikgPT4ge1xuICAgIGNvbnN0IHRyYW5zZm9ybWVyID0gc3VwZXJKc29uLmN1c3RvbVRyYW5zZm9ybWVyUmVnaXN0cnkuZmluZEFwcGxpY2FibGUodmFsdWUpO1xuICAgIHJldHVybiBbJ2N1c3RvbScsIHRyYW5zZm9ybWVyLm5hbWVdO1xufSwgKHZhbHVlLCBzdXBlckpzb24pID0+IHtcbiAgICBjb25zdCB0cmFuc2Zvcm1lciA9IHN1cGVySnNvbi5jdXN0b21UcmFuc2Zvcm1lclJlZ2lzdHJ5LmZpbmRBcHBsaWNhYmxlKHZhbHVlKTtcbiAgICByZXR1cm4gdHJhbnNmb3JtZXIuc2VyaWFsaXplKHZhbHVlKTtcbn0sICh2LCBhLCBzdXBlckpzb24pID0+IHtcbiAgICBjb25zdCB0cmFuc2Zvcm1lciA9IHN1cGVySnNvbi5jdXN0b21UcmFuc2Zvcm1lclJlZ2lzdHJ5LmZpbmRCeU5hbWUoYVsxXSk7XG4gICAgaWYgKCF0cmFuc2Zvcm1lcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1RyeWluZyB0byBkZXNlcmlhbGl6ZSB1bmtub3duIGN1c3RvbSB2YWx1ZScpO1xuICAgIH1cbiAgICByZXR1cm4gdHJhbnNmb3JtZXIuZGVzZXJpYWxpemUodik7XG59KTtcbmNvbnN0IGNvbXBvc2l0ZVJ1bGVzID0gW2NsYXNzUnVsZSwgc3ltYm9sUnVsZSwgY3VzdG9tUnVsZSwgdHlwZWRBcnJheVJ1bGVdO1xuZXhwb3J0IGNvbnN0IHRyYW5zZm9ybVZhbHVlID0gKHZhbHVlLCBzdXBlckpzb24pID0+IHtcbiAgICBjb25zdCBhcHBsaWNhYmxlQ29tcG9zaXRlUnVsZSA9IGZpbmRBcnIoY29tcG9zaXRlUnVsZXMsIHJ1bGUgPT4gcnVsZS5pc0FwcGxpY2FibGUodmFsdWUsIHN1cGVySnNvbikpO1xuICAgIGlmIChhcHBsaWNhYmxlQ29tcG9zaXRlUnVsZSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdmFsdWU6IGFwcGxpY2FibGVDb21wb3NpdGVSdWxlLnRyYW5zZm9ybSh2YWx1ZSwgc3VwZXJKc29uKSxcbiAgICAgICAgICAgIHR5cGU6IGFwcGxpY2FibGVDb21wb3NpdGVSdWxlLmFubm90YXRpb24odmFsdWUsIHN1cGVySnNvbiksXG4gICAgICAgIH07XG4gICAgfVxuICAgIGNvbnN0IGFwcGxpY2FibGVTaW1wbGVSdWxlID0gZmluZEFycihzaW1wbGVSdWxlcywgcnVsZSA9PiBydWxlLmlzQXBwbGljYWJsZSh2YWx1ZSwgc3VwZXJKc29uKSk7XG4gICAgaWYgKGFwcGxpY2FibGVTaW1wbGVSdWxlKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB2YWx1ZTogYXBwbGljYWJsZVNpbXBsZVJ1bGUudHJhbnNmb3JtKHZhbHVlLCBzdXBlckpzb24pLFxuICAgICAgICAgICAgdHlwZTogYXBwbGljYWJsZVNpbXBsZVJ1bGUuYW5ub3RhdGlvbixcbiAgICAgICAgfTtcbiAgICB9XG4gICAgcmV0dXJuIHVuZGVmaW5lZDtcbn07XG5jb25zdCBzaW1wbGVSdWxlc0J5QW5ub3RhdGlvbiA9IHt9O1xuc2ltcGxlUnVsZXMuZm9yRWFjaChydWxlID0+IHtcbiAgICBzaW1wbGVSdWxlc0J5QW5ub3RhdGlvbltydWxlLmFubm90YXRpb25dID0gcnVsZTtcbn0pO1xuZXhwb3J0IGNvbnN0IHVudHJhbnNmb3JtVmFsdWUgPSAoanNvbiwgdHlwZSwgc3VwZXJKc29uKSA9PiB7XG4gICAgaWYgKGlzQXJyYXkodHlwZSkpIHtcbiAgICAgICAgc3dpdGNoICh0eXBlWzBdKSB7XG4gICAgICAgICAgICBjYXNlICdzeW1ib2wnOlxuICAgICAgICAgICAgICAgIHJldHVybiBzeW1ib2xSdWxlLnVudHJhbnNmb3JtKGpzb24sIHR5cGUsIHN1cGVySnNvbik7XG4gICAgICAgICAgICBjYXNlICdjbGFzcyc6XG4gICAgICAgICAgICAgICAgcmV0dXJuIGNsYXNzUnVsZS51bnRyYW5zZm9ybShqc29uLCB0eXBlLCBzdXBlckpzb24pO1xuICAgICAgICAgICAgY2FzZSAnY3VzdG9tJzpcbiAgICAgICAgICAgICAgICByZXR1cm4gY3VzdG9tUnVsZS51bnRyYW5zZm9ybShqc29uLCB0eXBlLCBzdXBlckpzb24pO1xuICAgICAgICAgICAgY2FzZSAndHlwZWQtYXJyYXknOlxuICAgICAgICAgICAgICAgIHJldHVybiB0eXBlZEFycmF5UnVsZS51bnRyYW5zZm9ybShqc29uLCB0eXBlLCBzdXBlckpzb24pO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1Vua25vd24gdHJhbnNmb3JtYXRpb246ICcgKyB0eXBlKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgY29uc3QgdHJhbnNmb3JtYXRpb24gPSBzaW1wbGVSdWxlc0J5QW5ub3RhdGlvblt0eXBlXTtcbiAgICAgICAgaWYgKCF0cmFuc2Zvcm1hdGlvbikge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdVbmtub3duIHRyYW5zZm9ybWF0aW9uOiAnICsgdHlwZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRyYW5zZm9ybWF0aW9uLnVudHJhbnNmb3JtKGpzb24sIHN1cGVySnNvbik7XG4gICAgfVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRyYW5zZm9ybWVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/transformer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/util.js":
/*!*********************************************!*\
  !*** ./node_modules/superjson/dist/util.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   findArr: () => (/* binding */ findArr),\n/* harmony export */   forEach: () => (/* binding */ forEach),\n/* harmony export */   includes: () => (/* binding */ includes)\n/* harmony export */ });\nfunction valuesOfObj(record) {\n    if ('values' in Object) {\n        // eslint-disable-next-line es5/no-es6-methods\n        return Object.values(record);\n    }\n    const values = [];\n    // eslint-disable-next-line no-restricted-syntax\n    for (const key in record) {\n        if (record.hasOwnProperty(key)) {\n            values.push(record[key]);\n        }\n    }\n    return values;\n}\nfunction find(record, predicate) {\n    const values = valuesOfObj(record);\n    if ('find' in values) {\n        // eslint-disable-next-line es5/no-es6-methods\n        return values.find(predicate);\n    }\n    const valuesNotNever = values;\n    for (let i = 0; i < valuesNotNever.length; i++) {\n        const value = valuesNotNever[i];\n        if (predicate(value)) {\n            return value;\n        }\n    }\n    return undefined;\n}\nfunction forEach(record, run) {\n    Object.entries(record).forEach(([key, value]) => run(value, key));\n}\nfunction includes(arr, value) {\n    return arr.indexOf(value) !== -1;\n}\nfunction findArr(record, predicate) {\n    for (let i = 0; i < record.length; i++) {\n        const value = record[i];\n        if (predicate(value)) {\n            return value;\n        }\n    }\n    return undefined;\n}\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/util.js\n");

/***/ })

};
;