/**
 * 主布局组件
 * 整合导航栏、侧边栏和主内容区
 */

'use client';

import { useState } from 'react';
import Navbar from './Navbar';
import Sidebar from './Sidebar';

interface MainLayoutProps {
  children: React.ReactNode;
  onNewPrompt?: () => void;
  onManageCategories?: () => void;
}

export default function MainLayout({ children, onNewPrompt, onManageCategories }: MainLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const handleMenuToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSidebarClose = () => {
    setSidebarOpen(false);
  };

  const handleCategorySelect = (categoryId: string | null) => {
    setSelectedCategoryId(categoryId);
    // 在移动端选择分类后关闭侧边栏
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  return (
    <div className="h-screen flex overflow-hidden bg-gray-50">
      {/* 侧边栏 */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={handleSidebarClose}
        selectedCategoryId={selectedCategoryId}
        onCategorySelect={handleCategorySelect}
        onNewPrompt={onNewPrompt}
        onManageCategories={onManageCategories}
      />

      {/* 主内容区 */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* 导航栏 */}
        <Navbar
          onMenuToggle={handleMenuToggle}
          onSearchChange={handleSearchChange}
        />

        {/* 主内容 */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
