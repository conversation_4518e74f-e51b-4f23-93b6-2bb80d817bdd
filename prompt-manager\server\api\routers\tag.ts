/**
 * 标签管理路由器
 * 处理标签的CRUD操作
 */

import { z } from 'zod';
import { createTRPCRouter, protectedProcedure } from '~/server/api/trpc';

export const tagRouter = createTRPCRouter({
  /**
   * 获取所有标签
   */
  getAll: protectedProcedure.query(async ({ ctx }) => {
    return ctx.db.tag.findMany({
      include: {
        _count: {
          select: {
            prompts: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });
  }),

  /**
   * 获取用户使用的标签
   */
  getUserTags: protectedProcedure.query(async ({ ctx }) => {
    const tags = await ctx.db.tag.findMany({
      where: {
        prompts: {
          some: {
            prompt: {
              userId: ctx.session.user.id,
            },
          },
        },
      },
      include: {
        _count: {
          select: {
            prompts: {
              where: {
                prompt: {
                  userId: ctx.session.user.id,
                },
              },
            },
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    return tags;
  }),

  /**
   * 搜索标签（用于自动完成）
   */
  search: protectedProcedure
    .input(z.object({ query: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      return ctx.db.tag.findMany({
        where: {
          name: {
            contains: input.query,
            mode: 'insensitive',
          },
        },
        take: 10,
        orderBy: {
          name: 'asc',
        },
      });
    }),

  /**
   * 创建标签
   */
  create: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, '标签名称不能为空').max(50, '标签名称不能超过50个字符'),
        color: z.string().regex(/^#[0-9A-F]{6}$/i, '颜色格式不正确').default('#10B981'),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 检查标签是否已存在
      const existingTag = await ctx.db.tag.findUnique({
        where: { name: input.name },
      });

      if (existingTag) {
        return existingTag; // 如果已存在，直接返回
      }

      return ctx.db.tag.create({
        data: input,
      });
    }),

  /**
   * 更新标签
   */
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1, '标签名称不能为空').max(50, '标签名称不能超过50个字符').optional(),
        color: z.string().regex(/^#[0-9A-F]{6}$/i, '颜色格式不正确').optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 检查标签是否存在
      const tag = await ctx.db.tag.findUnique({
        where: { id },
      });

      if (!tag) {
        throw new Error('标签不存在');
      }

      // 如果更新名称，检查是否重复
      if (updateData.name && updateData.name !== tag.name) {
        const existingTag = await ctx.db.tag.findUnique({
          where: { name: updateData.name },
        });

        if (existingTag) {
          throw new Error('标签名称已存在');
        }
      }

      return ctx.db.tag.update({
        where: { id },
        data: updateData,
      });
    }),

  /**
   * 删除标签
   */
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 检查标签是否存在
      const tag = await ctx.db.tag.findUnique({
        where: { id: input.id },
      });

      if (!tag) {
        throw new Error('标签不存在');
      }

      // 删除标签（关联关系会自动删除）
      return ctx.db.tag.delete({
        where: { id: input.id },
      });
    }),
});
