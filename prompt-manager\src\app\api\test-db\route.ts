/**
 * 测试数据库连接的API端点
 */

import { NextRequest, NextResponse } from 'next/server';
import { db } from '~/lib/db';

export async function GET(request: NextRequest) {
  try {
    console.log('测试数据库连接...');
    
    // 测试数据库连接
    const userCount = await db.user.count();
    const categoryCount = await db.category.count();
    const promptCount = await db.prompt.count();
    
    console.log('数据库连接成功，统计结果:', {
      users: userCount,
      categories: categoryCount,
      prompts: promptCount,
    });
    
    return NextResponse.json({
      success: true,
      message: '数据库连接成功',
      data: {
        users: userCount,
        categories: categoryCount,
        prompts: promptCount,
      },
    });
  } catch (error) {
    console.error('数据库连接失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '数据库连接失败',
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
