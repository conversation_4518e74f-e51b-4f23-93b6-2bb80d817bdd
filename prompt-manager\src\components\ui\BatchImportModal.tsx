/**
 * 批量导入模态框组件
 * 支持JSON文件导入提示词
 */

'use client';

import { useState, useRef } from 'react';
import Modal from './Modal';
import { useToast } from './ToastContainer';

interface ImportData {
  title: string;
  content: string;
  description?: string;
  category?: string;
  tags?: string[];
}

interface BatchImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (data: ImportData[]) => Promise<void>;
}

export default function BatchImportModal({
  isOpen,
  onClose,
  onImport,
}: BatchImportModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [importData, setImportData] = useState<ImportData[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [importProgress, setImportProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showSuccess, showError } = useToast();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type !== 'application/json') {
        showError('文件格式错误', '请选择JSON格式的文件');
        return;
      }
      setFile(selectedFile);
      parseFile(selectedFile);
    }
  };

  const parseFile = async (file: File) => {
    try {
      const text = await file.text();
      const data = JSON.parse(text);
      
      // 验证数据格式
      const errors = validateImportData(data);
      if (errors.length > 0) {
        setValidationErrors(errors);
        setImportData([]);
        return;
      }

      setImportData(Array.isArray(data) ? data : [data]);
      setValidationErrors([]);
    } catch (error) {
      showError('文件解析失败', '请检查JSON文件格式是否正确');
      setImportData([]);
      setValidationErrors(['JSON格式错误']);
    }
  };

  const validateImportData = (data: any): string[] => {
    const errors: string[] = [];
    
    if (!Array.isArray(data) && typeof data !== 'object') {
      errors.push('数据格式错误：应为对象或对象数组');
      return errors;
    }

    const items = Array.isArray(data) ? data : [data];
    
    items.forEach((item, index) => {
      if (!item.title || typeof item.title !== 'string') {
        errors.push(`第${index + 1}项：缺少标题或标题格式错误`);
      }
      if (!item.content || typeof item.content !== 'string') {
        errors.push(`第${index + 1}项：缺少内容或内容格式错误`);
      }
      if (item.tags && !Array.isArray(item.tags)) {
        errors.push(`第${index + 1}项：标签格式错误，应为字符串数组`);
      }
    });

    return errors;
  };

  const handleImport = async () => {
    if (importData.length === 0) return;

    setIsProcessing(true);
    setImportProgress(0);

    try {
      // 模拟导入进度
      for (let i = 0; i <= 100; i += 10) {
        setImportProgress(i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      await onImport(importData);
      showSuccess('导入成功', `成功导入${importData.length}个提示词`);
      handleClose();
    } catch (error) {
      showError('导入失败', '请稍后重试');
    } finally {
      setIsProcessing(false);
      setImportProgress(0);
    }
  };

  const handleClose = () => {
    setFile(null);
    setImportData([]);
    setValidationErrors([]);
    setImportProgress(0);
    setIsProcessing(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onClose();
  };

  const downloadTemplate = () => {
    const template = [
      {
        title: '示例提示词1',
        content: '这是提示词的内容...',
        description: '这是提示词的描述（可选）',
        category: '分类名称（可选）',
        tags: ['标签1', '标签2']
      },
      {
        title: '示例提示词2',
        content: '另一个提示词的内容...',
        description: '另一个描述',
        category: '另一个分类',
        tags: ['标签3']
      }
    ];

    const blob = new Blob([JSON.stringify(template, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'prompt-template.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="批量导入提示词"
      size="lg"
    >
      <div className="space-y-6">
        {/* 文件上传区域 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            选择JSON文件
          </label>
          <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
            <div className="space-y-1 text-center">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                stroke="currentColor"
                fill="none"
                viewBox="0 0 48 48"
              >
                <path
                  d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <div className="flex text-sm text-gray-600">
                <label
                  htmlFor="file-upload"
                  className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                >
                  <span>选择文件</span>
                  <input
                    id="file-upload"
                    ref={fileInputRef}
                    name="file-upload"
                    type="file"
                    accept=".json"
                    className="sr-only"
                    onChange={handleFileSelect}
                  />
                </label>
                <p className="pl-1">或拖拽到此处</p>
              </div>
              <p className="text-xs text-gray-500">仅支持JSON格式文件</p>
            </div>
          </div>
          
          {file && (
            <div className="mt-2 text-sm text-gray-600">
              已选择文件: {file.name}
            </div>
          )}
        </div>

        {/* 模板下载 */}
        <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
          <div>
            <h4 className="text-sm font-medium text-blue-900">需要模板文件？</h4>
            <p className="text-sm text-blue-700">下载示例模板了解正确的数据格式</p>
          </div>
          <button
            onClick={downloadTemplate}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            下载模板
          </button>
        </div>

        {/* 验证错误 */}
        {validationErrors.length > 0 && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <h4 className="text-sm font-medium text-red-800 mb-2">数据验证错误：</h4>
            <ul className="text-sm text-red-700 space-y-1">
              {validationErrors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* 导入预览 */}
        {importData.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              预览数据 ({importData.length} 个提示词)
            </h4>
            <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-lg">
              {importData.slice(0, 5).map((item, index) => (
                <div key={index} className="p-3 border-b border-gray-100 last:border-b-0">
                  <div className="font-medium text-sm text-gray-900">{item.title}</div>
                  <div className="text-xs text-gray-500 mt-1 truncate">
                    {item.content.substring(0, 100)}...
                  </div>
                </div>
              ))}
              {importData.length > 5 && (
                <div className="p-3 text-center text-sm text-gray-500">
                  还有 {importData.length - 5} 个提示词...
                </div>
              )}
            </div>
          </div>
        )}

        {/* 导入进度 */}
        {isProcessing && (
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>导入进度</span>
              <span>{importProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${importProgress}%` }}
              />
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            onClick={handleClose}
            disabled={isProcessing}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            取消
          </button>
          <button
            onClick={handleImport}
            disabled={importData.length === 0 || isProcessing || validationErrors.length > 0}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? '导入中...' : `导入 ${importData.length} 个提示词`}
          </button>
        </div>
      </div>
    </Modal>
  );
}
