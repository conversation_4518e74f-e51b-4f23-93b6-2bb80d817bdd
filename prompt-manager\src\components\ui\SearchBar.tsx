/**
 * 搜索栏组件
 * 包含搜索输入框、筛选器和排序选项
 */

'use client';

import { useState, useRef, useEffect } from 'react';

interface Tag {
  id: string;
  name: string;
  color: string;
}

interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
}

interface SearchFilters {
  query: string;
  categoryId: string | null;
  tags: string[];
  sortBy: 'createdAt' | 'updatedAt' | 'usageCount' | 'title';
  sortOrder: 'asc' | 'desc';
}

interface SearchBarProps {
  filters: SearchFilters;
  categories?: Category[];
  tags?: Tag[];
  onFiltersChange: (filters: SearchFilters) => void;
  placeholder?: string;
}

export default function SearchBar({
  filters,
  categories = [],
  tags = [],
  onFiltersChange,
  placeholder = '搜索提示词...',
}: SearchBarProps) {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [tagSearchQuery, setTagSearchQuery] = useState('');
  const filterRef = useRef<HTMLDivElement>(null);

  // 模拟数据
  const mockCategories: Category[] = [
    { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️' },
    { id: '2', name: '代码生成', color: '#10B981', icon: '💻' },
    { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐' },
  ];

  const mockTags: Tag[] = [
    { id: '1', name: 'AI', color: '#3B82F6' },
    { id: '2', name: '编程', color: '#10B981' },
    { id: '3', name: '创意', color: '#F59E0B' },
    { id: '4', name: '商务', color: '#EF4444' },
  ];

  const displayCategories = categories.length > 0 ? categories : mockCategories;
  const displayTags = tags.length > 0 ? tags : mockTags;

  // 点击外部关闭筛选器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setIsFilterOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFiltersChange({
      ...filters,
      query: e.target.value,
    });
  };

  const handleCategoryChange = (categoryId: string | null) => {
    onFiltersChange({
      ...filters,
      categoryId,
    });
  };

  const handleTagToggle = (tagId: string) => {
    const newTags = filters.tags.includes(tagId)
      ? filters.tags.filter(id => id !== tagId)
      : [...filters.tags, tagId];
    
    onFiltersChange({
      ...filters,
      tags: newTags,
    });
  };

  const handleSortChange = (sortBy: SearchFilters['sortBy'], sortOrder: SearchFilters['sortOrder']) => {
    onFiltersChange({
      ...filters,
      sortBy,
      sortOrder,
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      query: '',
      categoryId: null,
      tags: [],
      sortBy: 'updatedAt',
      sortOrder: 'desc',
    });
  };

  const filteredTags = displayTags.filter(tag =>
    tag.name.toLowerCase().includes(tagSearchQuery.toLowerCase())
  );

  const hasActiveFilters = filters.categoryId || filters.tags.length > 0;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex flex-col space-y-4">
        {/* 搜索输入框 */}
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder={placeholder}
              value={filters.query}
              onChange={handleQueryChange}
            />
          </div>

          {/* 筛选按钮 */}
          <div className="relative" ref={filterRef}>
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                hasActiveFilters
                  ? 'bg-blue-50 text-blue-700 border-blue-300'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
              </svg>
              筛选
              {hasActiveFilters && (
                <span className="ml-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-blue-600 rounded-full">
                  {(filters.categoryId ? 1 : 0) + filters.tags.length}
                </span>
              )}
            </button>

            {/* 筛选面板 */}
            {isFilterOpen && (
              <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-20 border border-gray-200">
                <div className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">筛选选项</h3>
                    {hasActiveFilters && (
                      <button
                        onClick={clearFilters}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        清除筛选
                      </button>
                    )}
                  </div>

                  {/* 分类筛选 */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">分类</label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="category"
                          checked={filters.categoryId === null}
                          onChange={() => handleCategoryChange(null)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700">全部分类</span>
                      </label>
                      {displayCategories.map((category) => (
                        <label key={category.id} className="flex items-center">
                          <input
                            type="radio"
                            name="category"
                            checked={filters.categoryId === category.id}
                            onChange={() => handleCategoryChange(category.id)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <span className="ml-2 flex items-center text-sm text-gray-700">
                            <span className="mr-1">{category.icon}</span>
                            {category.name}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* 标签筛选 */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">标签</label>
                    <input
                      type="text"
                      placeholder="搜索标签..."
                      value={tagSearchQuery}
                      onChange={(e) => setTagSearchQuery(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 mb-2"
                    />
                    <div className="max-h-32 overflow-y-auto space-y-2">
                      {filteredTags.map((tag) => (
                        <label key={tag.id} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={filters.tags.includes(tag.id)}
                            onChange={() => handleTagToggle(tag.id)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span
                            className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white"
                            style={{ backgroundColor: tag.color }}
                          >
                            {tag.name}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* 排序选项 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">排序</label>
                    <div className="grid grid-cols-2 gap-2">
                      <select
                        value={filters.sortBy}
                        onChange={(e) => handleSortChange(e.target.value as SearchFilters['sortBy'], filters.sortOrder)}
                        className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="updatedAt">更新时间</option>
                        <option value="createdAt">创建时间</option>
                        <option value="usageCount">使用次数</option>
                        <option value="title">标题</option>
                      </select>
                      <select
                        value={filters.sortOrder}
                        onChange={(e) => handleSortChange(filters.sortBy, e.target.value as SearchFilters['sortOrder'])}
                        className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="desc">降序</option>
                        <option value="asc">升序</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 活跃筛选器显示 */}
        {hasActiveFilters && (
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-sm text-gray-500">筛选条件:</span>
            {filters.categoryId && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                {displayCategories.find(c => c.id === filters.categoryId)?.name}
                <button
                  onClick={() => handleCategoryChange(null)}
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 text-blue-400 hover:text-blue-600"
                >
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </span>
            )}
            {filters.tags.map((tagId) => {
              const tag = displayTags.find(t => t.id === tagId);
              return tag ? (
                <span
                  key={tagId}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm text-white"
                  style={{ backgroundColor: tag.color }}
                >
                  {tag.name}
                  <button
                    onClick={() => handleTagToggle(tagId)}
                    className="ml-2 inline-flex items-center justify-center w-4 h-4 text-white hover:text-gray-200"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </span>
              ) : null;
            })}
          </div>
        )}
      </div>
    </div>
  );
}
