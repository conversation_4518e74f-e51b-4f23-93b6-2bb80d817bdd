/**
 * 数据库连接配置
 * 使用Prisma客户端进行数据库操作
 */

import { PrismaClient } from '@prisma/client';

// 全局变量声明，避免在开发环境中重复创建连接
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// 创建Prisma客户端实例
export const db =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: ['query', 'error', 'warn'],
  });

// 在开发环境中将实例保存到全局变量，避免热重载时重复创建连接
if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db;

// 导出类型
export type Database = typeof db;
