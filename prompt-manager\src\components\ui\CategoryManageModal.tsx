/**
 * 分类管理模态框组件
 * 用于管理提示词分类
 */

'use client';

import { useState, useEffect } from 'react';
import Modal from './Modal';

interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
  count?: number;
}

interface CategoryManageModalProps {
  isOpen: boolean;
  onClose: () => void;
  categories?: Category[];
  onSave: (categoryData: {
    name: string;
    description?: string;
    color: string;
    icon: string;
  }) => void;
  onUpdate: (id: string, categoryData: {
    name: string;
    description?: string;
    color: string;
    icon: string;
  }) => void;
  onDelete: (id: string) => void;
}

export default function CategoryManageModal({
  isOpen,
  onClose,
  categories = [],
  onSave,
  onUpdate,
  onDelete,
}: CategoryManageModalProps) {
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#3B82F6',
    icon: '📁',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 预设颜色选项
  const colorOptions = [
    '#3B82F6', // 蓝色
    '#10B981', // 绿色
    '#F59E0B', // 黄色
    '#EF4444', // 红色
    '#8B5CF6', // 紫色
    '#F97316', // 橙色
    '#06B6D4', // 青色
    '#84CC16', // 石灰色
  ];

  // 预设图标选项
  const iconOptions = [
    '📁', '✍️', '💻', '🌐', '📊', '🎨', '🔧', '📚',
    '💡', '🎯', '🚀', '⚡', '🔥', '💎', '🎪', '🎭',
  ];

  // 模拟数据
  const mockCategories: Category[] = [
    { id: '1', name: '写作助手', color: '#3B82F6', icon: '✍️', count: 12 },
    { id: '2', name: '代码生成', color: '#10B981', icon: '💻', count: 8 },
    { id: '3', name: '翻译工具', color: '#F59E0B', icon: '🌐', count: 5 },
  ];

  const displayCategories = categories.length > 0 ? categories : mockCategories;

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      color: '#3B82F6',
      icon: '📁',
    });
    setEditingCategory(null);
    setErrors({});
  };

  // 开始编辑分类
  const startEdit = (category: Category) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: '',
      color: category.color,
      icon: category.icon,
    });
    setErrors({});
  };

  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = '分类名称不能为空';
    } else if (formData.name.length > 50) {
      newErrors.name = '分类名称不能超过50个字符';
    }

    // 检查名称是否重复
    const isDuplicate = displayCategories.some(
      cat => cat.name === formData.name.trim() && cat.id !== editingCategory?.id
    );
    if (isDuplicate) {
      newErrors.name = '分类名称已存在';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交表单
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    const categoryData = {
      name: formData.name.trim(),
      description: formData.description.trim() || undefined,
      color: formData.color,
      icon: formData.icon,
    };

    if (editingCategory) {
      onUpdate(editingCategory.id, categoryData);
    } else {
      onSave(categoryData);
    }

    resetForm();
  };

  // 删除分类
  const handleDelete = (category: Category) => {
    if (window.confirm(`确定要删除分类"${category.name}"吗？${category.count ? `这将影响${category.count}个提示词。` : ''}`)) {
      onDelete(category.id);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="管理分类"
      size="lg"
    >
      <div className="space-y-6">
        {/* 分类列表 */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">现有分类</h3>
          <div className="space-y-2">
            {displayCategories.map((category) => (
              <div
                key={category.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <span
                    className="inline-flex items-center justify-center w-8 h-8 rounded-full text-white text-sm font-medium"
                    style={{ backgroundColor: category.color }}
                  >
                    {category.icon}
                  </span>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">{category.name}</h4>
                    {category.count !== undefined && (
                      <p className="text-xs text-gray-500">{category.count} 个提示词</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => startEdit(category)}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    编辑
                  </button>
                  <button
                    onClick={() => handleDelete(category)}
                    className="text-red-600 hover:text-red-800 text-sm font-medium"
                  >
                    删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 添加/编辑分类表单 */}
        <div className="border-t border-gray-200 pt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {editingCategory ? '编辑分类' : '添加新分类'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 分类名称 */}
            <div>
              <label htmlFor="categoryName" className="block text-sm font-medium text-gray-700 mb-2">
                分类名称 *
              </label>
              <input
                type="text"
                id="categoryName"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="输入分类名称"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            {/* 分类描述 */}
            <div>
              <label htmlFor="categoryDescription" className="block text-sm font-medium text-gray-700 mb-2">
                描述
              </label>
              <textarea
                id="categoryDescription"
                rows={2}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="简要描述这个分类"
              />
            </div>

            {/* 颜色选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">颜色</label>
              <div className="flex flex-wrap gap-2">
                {colorOptions.map((color) => (
                  <button
                    key={color}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, color }))}
                    className={`w-8 h-8 rounded-full border-2 ${
                      formData.color === color ? 'border-gray-400' : 'border-gray-200'
                    }`}
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>

            {/* 图标选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">图标</label>
              <div className="grid grid-cols-8 gap-2">
                {iconOptions.map((icon) => (
                  <button
                    key={icon}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, icon }))}
                    className={`w-10 h-10 rounded-md border-2 flex items-center justify-center text-lg ${
                      formData.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    {icon}
                  </button>
                ))}
              </div>
            </div>

            {/* 预览 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">预览</label>
              <div className="inline-flex items-center space-x-2 px-3 py-2 bg-gray-50 rounded-lg">
                <span
                  className="inline-flex items-center justify-center w-6 h-6 rounded-full text-white text-sm"
                  style={{ backgroundColor: formData.color }}
                >
                  {formData.icon}
                </span>
                <span className="text-sm font-medium text-gray-900">
                  {formData.name || '分类名称'}
                </span>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-3">
              {editingCategory && (
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  取消编辑
                </button>
              )}
              <button
                type="submit"
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {editingCategory ? '保存更改' : '添加分类'}
              </button>
            </div>
          </form>
        </div>

        {/* 关闭按钮 */}
        <div className="flex justify-end pt-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            关闭
          </button>
        </div>
      </div>
    </Modal>
  );
}
