/**
 * 分类管理路由器
 * 处理分类的CRUD操作
 */

import { z } from 'zod';
import { createTRPCRouter, protectedProcedure } from '~/server/api/trpc';

// 输入验证schema
const createCategorySchema = z.object({
  name: z.string().min(1, '分类名称不能为空').max(50, '分类名称不能超过50个字符'),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, '颜色格式不正确').default('#3B82F6'),
  icon: z.string().default('folder'),
});

const updateCategorySchema = z.object({
  id: z.string(),
  name: z.string().min(1, '分类名称不能为空').max(50, '分类名称不能超过50个字符').optional(),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, '颜色格式不正确').optional(),
  icon: z.string().optional(),
});

export const categoryRouter = createTRPCRouter({
  /**
   * 获取用户所有分类
   */
  getAll: protectedProcedure.query(async ({ ctx }) => {
    return ctx.db.category.findMany({
      where: {
        userId: ctx.session.user.id,
      },
      include: {
        _count: {
          select: {
            prompts: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });
  }),

  /**
   * 根据ID获取分类详情
   */
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const category = await ctx.db.category.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
        include: {
          _count: {
            select: {
              prompts: true,
            },
          },
        },
      });

      if (!category) {
        throw new Error('分类不存在');
      }

      return category;
    }),

  /**
   * 创建新分类
   */
  create: protectedProcedure
    .input(createCategorySchema)
    .mutation(async ({ ctx, input }) => {
      // 检查分类名称是否已存在
      const existingCategory = await ctx.db.category.findFirst({
        where: {
          name: input.name,
          userId: ctx.session.user.id,
        },
      });

      if (existingCategory) {
        throw new Error('分类名称已存在');
      }

      return ctx.db.category.create({
        data: {
          ...input,
          userId: ctx.session.user.id,
        },
      });
    }),

  /**
   * 更新分类
   */
  update: protectedProcedure
    .input(updateCategorySchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 验证分类所有权
      const category = await ctx.db.category.findFirst({
        where: {
          id,
          userId: ctx.session.user.id,
        },
      });

      if (!category) {
        throw new Error('分类不存在');
      }

      // 如果更新名称，检查是否重复
      if (updateData.name && updateData.name !== category.name) {
        const existingCategory = await ctx.db.category.findFirst({
          where: {
            name: updateData.name,
            userId: ctx.session.user.id,
            id: { not: id },
          },
        });

        if (existingCategory) {
          throw new Error('分类名称已存在');
        }
      }

      return ctx.db.category.update({
        where: { id },
        data: updateData,
      });
    }),

  /**
   * 删除分类
   */
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 验证分类所有权
      const category = await ctx.db.category.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
      });

      if (!category) {
        throw new Error('分类不存在');
      }

      // 删除分类（关联的提示词会自动设置categoryId为null）
      return ctx.db.category.delete({
        where: { id: input.id },
      });
    }),
});
