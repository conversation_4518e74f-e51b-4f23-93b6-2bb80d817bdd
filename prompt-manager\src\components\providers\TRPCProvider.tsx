/**
 * tRPC Provider组件
 * 为应用提供tRPC客户端和React Query缓存
 */

'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { api, queryClient, getTRPCClient } from '~/lib/trpc';
import { useState } from 'react';

interface TRPCProviderProps {
  children: React.ReactNode;
}

export default function TRPCProvider({ children }: TRPCProviderProps) {
  const [trpcClient] = useState(() => getTRPCClient());

  return (
    <api.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        {children}
        {/* 开发环境显示React Query DevTools */}
        {process.env.NODE_ENV === 'development' && (
          <ReactQueryDevtools initialIsOpen={false} />
        )}
      </QueryClientProvider>
    </api.Provider>
  );
}
