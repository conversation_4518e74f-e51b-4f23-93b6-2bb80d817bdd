/**
 * 全局错误处理Context
 * 提供统一的错误处理和用户通知机制
 */

'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { useToast } from '@/components/ui/ToastContainer';
import { getErrorMessage, isNetworkError, isAuthError } from '@/components/error/ErrorBoundary';

interface ErrorInfo {
  id: string;
  error: Error;
  timestamp: Date;
  context?: string;
  handled: boolean;
}

interface ErrorContextType {
  errors: ErrorInfo[];
  reportError: (error: Error, context?: string) => void;
  clearError: (id: string) => void;
  clearAllErrors: () => void;
  handleApiError: (error: unknown, context?: string) => void;
  handleNetworkError: (error: Error, context?: string) => void;
  handleAuthError: (error: Error, context?: string) => void;
}

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

interface ErrorProviderProps {
  children: ReactNode;
}

export function ErrorProvider({ children }: ErrorProviderProps) {
  const [errors, setErrors] = useState<ErrorInfo[]>([]);
  const { showError, showWarning, showInfo } = useToast();

  const generateId = useCallback(() => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }, []);

  const reportError = useCallback((error: Error, context?: string) => {
    const errorInfo: ErrorInfo = {
      id: generateId(),
      error,
      timestamp: new Date(),
      context,
      handled: false,
    };

    setErrors(prev => [...prev, errorInfo]);

    // 记录到控制台
    console.error('Error reported:', error, { context, timestamp: errorInfo.timestamp });

    // 在生产环境中发送到错误报告服务
    if (process.env.NODE_ENV === 'production') {
      // 例如：发送到Sentry、LogRocket等服务
      // errorReportingService.captureException(error, { extra: { context } });
    }

    return errorInfo.id;
  }, [generateId]);

  const clearError = useCallback((id: string) => {
    setErrors(prev => prev.filter(error => error.id !== id));
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrors([]);
  }, []);

  const handleApiError = useCallback((error: unknown, context?: string) => {
    const errorMessage = getErrorMessage(error);
    const errorObj = error instanceof Error ? error : new Error(errorMessage);

    // 根据错误类型显示不同的提示
    if (isAuthError(error)) {
      showWarning('认证失败', '请重新登录后再试');
      // 可以在这里触发重定向到登录页面
      // router.push('/auth/signin');
    } else if (isNetworkError(error)) {
      showError('网络错误', '请检查网络连接后重试');
    } else if (errorMessage.includes('FORBIDDEN')) {
      showError('权限不足', '您没有权限执行此操作');
    } else if (errorMessage.includes('NOT_FOUND')) {
      showError('资源不存在', '请求的资源未找到');
    } else if (errorMessage.includes('CONFLICT')) {
      showError('操作冲突', '资源已被其他用户修改，请刷新后重试');
    } else if (errorMessage.includes('VALIDATION_ERROR')) {
      showError('数据验证失败', '请检查输入的数据格式');
    } else {
      showError('操作失败', errorMessage);
    }

    reportError(errorObj, context);
  }, [showError, showWarning, reportError]);

  const handleNetworkError = useCallback((error: Error, context?: string) => {
    showError('网络连接失败', '请检查网络连接后重试');
    reportError(error, context);
  }, [showError, reportError]);

  const handleAuthError = useCallback((error: Error, context?: string) => {
    showWarning('登录已过期', '请重新登录');
    reportError(error, context);
    
    // 清除本地存储的认证信息
    localStorage.removeItem('prompt-manager-storage');
    
    // 重定向到登录页面
    setTimeout(() => {
      window.location.href = '/auth/signin';
    }, 2000);
  }, [showWarning, reportError]);

  const value: ErrorContextType = {
    errors,
    reportError,
    clearError,
    clearAllErrors,
    handleApiError,
    handleNetworkError,
    handleAuthError,
  };

  return (
    <ErrorContext.Provider value={value}>
      {children}
    </ErrorContext.Provider>
  );
}

export function useErrorHandler() {
  const context = useContext(ErrorContext);
  if (context === undefined) {
    throw new Error('useErrorHandler must be used within an ErrorProvider');
  }
  return context;
}

/**
 * 高阶组件：为组件添加错误处理能力
 */
export function withErrorHandler<P extends object>(
  Component: React.ComponentType<P>,
  defaultContext?: string
) {
  return function WrappedComponent(props: P) {
    const { handleApiError } = useErrorHandler();

    const enhancedProps = {
      ...props,
      onError: (error: unknown) => {
        handleApiError(error, defaultContext);
      },
    };

    return <Component {...enhancedProps} />;
  };
}

/**
 * Hook：为异步操作添加错误处理
 */
export function useAsyncErrorHandler() {
  const { handleApiError } = useErrorHandler();

  const executeWithErrorHandling = useCallback(
    async <T>(
      asyncFn: () => Promise<T>,
      context?: string,
      customErrorHandler?: (error: unknown) => void
    ): Promise<T | null> => {
      try {
        return await asyncFn();
      } catch (error) {
        if (customErrorHandler) {
          customErrorHandler(error);
        } else {
          handleApiError(error, context);
        }
        return null;
      }
    },
    [handleApiError]
  );

  return { executeWithErrorHandling };
}

/**
 * Hook：为表单提交添加错误处理
 */
export function useFormErrorHandler() {
  const { handleApiError } = useErrorHandler();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const handleSubmit = useCallback(
    async <T>(
      submitFn: () => Promise<T>,
      options?: {
        context?: string;
        onSuccess?: (result: T) => void;
        onError?: (error: unknown) => void;
        showSuccessToast?: boolean;
      }
    ) => {
      setIsSubmitting(true);
      setSubmitError(null);

      try {
        const result = await submitFn();
        if (options?.onSuccess) {
          options.onSuccess(result);
        }
        return result;
      } catch (error) {
        const errorMessage = getErrorMessage(error);
        setSubmitError(errorMessage);
        
        if (options?.onError) {
          options.onError(error);
        } else {
          handleApiError(error, options?.context);
        }
        return null;
      } finally {
        setIsSubmitting(false);
      }
    },
    [handleApiError]
  );

  const clearSubmitError = useCallback(() => {
    setSubmitError(null);
  }, []);

  return {
    isSubmitting,
    submitError,
    handleSubmit,
    clearSubmitError,
  };
}

/**
 * 错误重试Hook
 */
export function useRetry() {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  const retry = useCallback(
    async <T>(
      asyncFn: () => Promise<T>,
      maxRetries: number = 3,
      delay: number = 1000
    ): Promise<T | null> => {
      setIsRetrying(true);
      
      for (let i = 0; i <= maxRetries; i++) {
        try {
          const result = await asyncFn();
          setRetryCount(0);
          setIsRetrying(false);
          return result;
        } catch (error) {
          setRetryCount(i + 1);
          
          if (i === maxRetries) {
            setIsRetrying(false);
            throw error;
          }
          
          // 等待指定时间后重试
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
        }
      }
      
      setIsRetrying(false);
      return null;
    },
    []
  );

  const resetRetry = useCallback(() => {
    setRetryCount(0);
    setIsRetrying(false);
  }, []);

  return {
    retry,
    retryCount,
    isRetrying,
    resetRetry,
  };
}
